// lib/i18n/context.tsx
"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Language, Translations, getTranslation, getLanguageInfo } from './index';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: Translations;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
  defaultLanguage?: Language;
}

export function LanguageProvider({ children, defaultLanguage = 'en' }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<Language>(defaultLanguage);

  // Load saved language from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('steribot-language') as Language;
      if (savedLanguage && ['en', 'fr', 'ar', 'es'].includes(savedLanguage)) {
        setLanguageState(savedLanguage);
      }
    }
  }, []);

  // Save language to localStorage and update document direction
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    if (typeof window !== 'undefined') {
      localStorage.setItem('steribot-language', newLanguage);
      const languageInfo = getLanguageInfo(newLanguage);
      document.documentElement.dir = languageInfo.rtl ? 'rtl' : 'ltr';
      document.documentElement.lang = newLanguage;
    }
  };

  // Update document direction when language changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const languageInfo = getLanguageInfo(language);
      document.documentElement.dir = languageInfo.rtl ? 'rtl' : 'ltr';
      document.documentElement.lang = language;
    }
  }, [language]);

  const t = getTranslation(language);
  const isRTL = getLanguageInfo(language).rtl || false;

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export function useTranslation() {
  const { t } = useLanguage();
  return t;
}
