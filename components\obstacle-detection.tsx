"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTranslation } from "@/lib/i18n/context"

export function ObstacleDetection() {
  const t = useTranslation()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Obstacle Detection</h1>
        <p className="text-muted-foreground">Static view - no API calls</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Detection Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Chair detected in path</h3>
                <p className="text-sm text-muted-foreground">Operating Room 1 - 5 min ago</p>
              </div>
              <Badge variant="secondary">Resolved</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Medical equipment blocking</h3>
                <p className="text-sm text-muted-foreground">ICU Ward - 10 min ago</p>
              </div>
              <Badge variant="destructive">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Person detected</h3>
                <p className="text-sm text-muted-foreground">Patient Room 101 - 15 min ago</p>
              </div>
              <Badge variant="secondary">Resolved</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
