"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Edit, Download, Shield, Bell, LogOut } from "lucide-react"
import { useTranslation } from "@/lib/i18n/context"

export function Profile() {
  const t = useTranslation();

  return (
    <div className="p-6 space-y-6 bg-teal-50 min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-teal-800">{t?.profile?.title || 'Profile'}</h1>
        <Button variant="outline" className="text-teal-600 border-teal-600 hover:bg-teal-50 bg-transparent">
          <Edit className="w-4 h-4 mr-2" />
          {t?.profile?.editProfile || 'Edit Profile'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Info */}
        <div className="space-y-6">
          {/* Main Profile Card */}
          <Card className=" text-white" style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center">
                <Avatar className="w-24 h-24 mb-4">
                  <AvatarImage src="/placeholder-user.jpg" alt="Mohamed Ali" />
                  <AvatarFallback className="bg-teal-600 text-white text-xl">MA</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold mb-2">Mohamed Ali</h2>
              </div>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle>{t?.profile?.accountStatus || 'Account Status'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{t?.profile?.accountStatus || 'Account Status'}</span>
                <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{t?.status?.active || 'Active'}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{t?.profile?.memberSince || 'Member Since'}</span>
                <span className="font-medium">{t?.profile?.january2024 || 'January 2024'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{t?.profile?.lastLogin || 'Last Login'}</span>
                <span className="font-medium">{t?.profile?.today || 'Today'}, 09:15 AM</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">{t?.profile?.accessLevel || 'Access Level'}</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {t?.profile?.administrator || 'Administrator'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Details and Actions */}
        <div className="space-y-6">
          {/* Details Card */}
          <Card>
            <CardHeader>
              <CardTitle>{t?.profile?.details || 'Details'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-gray-600">{t?.profile?.name || 'Name'}</label>
                  <p className="font-medium">Mohamed</p>
                </div>
                <div>
                  <label className="text-sm text-gray-600">{t?.profile?.lastName || 'Last Name'}</label>
                  <p className="font-medium">Ali</p>
                </div>
              </div>
              <div>
                <label className="text-sm text-gray-600">{t?.profile?.email || 'Email'}</label>
                <p className="font-medium"><EMAIL></p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-gray-600">{t?.profile?.jobTitle || 'Job Title'}</label>
                  <p className="font-medium">{t?.profile?.doctor || 'Doctor'}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-600">{t?.profile?.department || 'Department'}</label>
                  <p className="font-medium">{t?.profile?.cardiology || 'Cardiology'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <CardTitle>{t?.profile?.security || 'Security'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{t?.profile?.email || 'Email Address'}</p>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{t?.profile?.changePassword || 'Current Password'}</p>
                  <p className="text-sm text-gray-600">••••••••••••</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t?.profile?.securitySettings || 'Quick Actions'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Download className="w-4 h-4 mr-2" />
                {t?.profile?.downloadData || 'Download Activity Report'}
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Shield className="w-4 h-4 mr-2" />
                {t?.profile?.twoFactor || 'Two-Factor Authentication'}
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Bell className="w-4 h-4 mr-2" />
                {t?.profile?.notifications2FA || 'Notification Settings'}
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 bg-transparent"
              >
                <LogOut className="w-4 h-4 mr-2" />
                {t?.profile?.logoutAllDevices || 'Sign Out'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
