// lib/i18n/types.ts

export type Language = 'en' | 'fr' | 'ar' | 'es';

export interface LanguageOption {
  code: Language;
  name: string;
  nativeName: string;
  flag: string;
  rtl?: boolean;
}

export const LANGUAGES: LanguageOption[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false
  }
];

export interface Translations {
  // Login Page
  login: {
    title: string;
    subtitle: string;
    welcome: string;
    email: string;
    emailPlaceholder: string;
    password: string;
    passwordPlaceholder: string;
    connect: string;
    connecting: string;
    forgotPassword: string;
    emailRequired: string;
    emailInvalid: string;
    passwordRequired: string;
    passwordMinLength: string;
    invalidCredentials: string;
    networkError: string;
    authenticationFailed: string;
  };
  
  // Network Modal
  network: {
    title: string;
    subtitle: string;
    wifi: string;
    wifiDescription: string;
    mobile: string;
    mobileDescription: string;
    connect: string;
    connecting: string;
  };
  
  // Connecting Interface
  connecting: {
    title: string;
    subtitle: string;
    steps: {
      initializing: string;
      scanning: string;
      establishing: string;
      synchronizing: string;
      established: string;
    };
    complete: string;
    lookingFor: string;
    ensureDevice: string;
    wifi: string;
    mobileData: string;
  };
  
  // Robot Interface
  robot: {
    title: string;
    subtitle: string;
    connected: string;
    active: string;
    startScan: string;
  };
  
  // Navigation/Sidebar
  navigation: {
    home: string;
    dashboard: string;
    robotDetails: string;
    robotsList: string;
    sterilizationHistory: string;
    obstacleDetection: string;
    profile: string;
    logout: string;
    authTest: string;
  };

  // Home Page
  home: {
    title: string;
    subtitle: string;
    configurationScanning: string;
    robotStatus: string;
    parameters: string;
    mapView: string;
    cameraView: string;
    directionControls: string;
    reset: string;
    startSterilization: string;
    spaceConfiguration: string;
    sterilizationConfig: string;
    position: string;
    battery: string;
    speed: string;
    resolution: string;
    scanRange: string;
    updateRate: string;
    stopScan: string;
    saveMap: string;
    mapSaved: string;
    resetMap: string;
  };

  // Dashboard
  dashboard: {
    title: string;
    overview: string;
    statistics: string;
    activeConnections: string;
    totalUsers: string;
    sterilizedAreas: string;
    efficiency: string;
    bacteriaDetection: string;
    recentSessions: string;
    sessionHistory: string;
    date: string;
    duration: string;
    area: string;
    status: string;
    completed: string;
    inProgress: string;
    failed: string;
    todaysConnections: string;
    todaysUsers: string;
    sterilizedZones: string;
    fromSterilizedZones: string;
    operatingRoomA: string;
    surgeryRoom: string;
    mainCorridor: string;
    operatingRoomB: string;
    log: string;
    others: string;
    bacteriaTypes: {
      eColi: string;
      staphylococcus: string;
      pseudomonas: string;
      others: string;
    };
    bacteriaDetected24h: string;
    total: string;
    zone: string;
    sterilizationLogChart: string;
    dateTime: string;
    logStatus: string;
    firstLog: string;
    secondLog: string;
    thirdLog: string;
    fourthLog: string;
    fifthLog: string;
    sixthLog: string;
    activeRobots: string;
    totalRobots: string;
    criticalAlerts: string;
  };

  // Robot Details
  robotDetails: {
    title: string;
    information: string;
    model: string;
    serialNumber: string;
    status: string;
    batteryLevel: string;
    lastMaintenance: string;
    operatingHours: string;
    specifications: string;
    dimensions: string;
    weight: string;
    maxSpeed: string;
    uvLamps: string;
    coverage: string;
    maintenance: string;
    schedule: string;
    nextService: string;
    serviceHistory: string;
    weekDays: {
      mon: string;
      tue: string;
      wed: string;
      thu: string;
      fri: string;
      sat: string;
      sun: string;
    };
    months: {
      january: string;
      february: string;
      march: string;
      april: string;
      may: string;
      june: string;
      july: string;
      august: string;
      september: string;
      october: string;
      november: string;
      december: string;
    };
    scheduledTasks: string;
    operatingRoomSterilization: string;
    robotMaintenanceCheck: string;
    preSurgeryProtocol: string;
    weeklyMaintenance: string;
    scheduled: string;
    completed: string;
    inProgress: string;
    high: string;
    medium: string;
    low: string;
    sterilization: string;
    maintenanceType: string;
    operatingRoomA: string;
    robotStation: string;
    drSmith: string;
    techTeam: string;
    mainCorridorCleaning: string;
    emergencyRoomSterilization: string;
    dailyCorridorSterilization: string;
    postIncidentSterilization: string;
    mainCorridor: string;
    emergencyRoom: string;
    nightShift: string;
    drJohnson: string;
    showCalendar: string;
    hideCalendar: string;
    edit: string;
    day: string;
    week: string;
    month: string;
    year: string;
    addTask: string;
    taskTitle: string;
    date: string;
    time: string;
    duration: string;
    priority: string;
    roomLocation: string;
    assignedTo: string;
    notes: string;
    create: string;
    cancel: string;
    editTask: string;
    deleteTask: string;
    save: string;
    minutes: string;
  };

  // Robots List
  robotsList: {
    title: string;
    addRobot: string;
    addSpace: string;
    robotDescription: string;
    startSterilization: string;
    name: string;
    location: string;
    description: string;
    roomType: string;
    area: string;
    addRobotTitle: string;
    addSpaceTitle: string;
    cancel: string;
    add: string;
    addNewRobot: string;
    robotName: string;
    enterRobotName: string;
    selectModel: string;
    serialNumber: string;
    enterSerialNumber: string;
    enterLocation: string;
    enterDescription: string;
    addNewSpace: string;
    spaceName: string;
    enterSpaceName: string;
    selectRoomType: string;
    enterArea: string;
    operatingRoom: string;
    surgeryRoom: string;
    corridor: string;
    laboratory: string;
  };

  // Profile
  profile: {
    title: string;
    personalInfo: string;
    name: string;
    email: string;
    role: string;
    department: string;
    phone: string;
    settings: string;
    notifications: string;
    language: string;
    timezone: string;
    theme: string;
    security: string;
    changePassword: string;
    twoFactor: string;
    loginHistory: string;
    editProfile: string;
    accountStatus: string;
    memberSince: string;
    lastLogin: string;
    accessLevel: string;
    administrator: string;
    details: string;
    lastName: string;
    jobTitle: string;
    doctor: string;
    cardiology: string;
    securitySettings: string;
    downloadData: string;
    notifications2FA: string;
    logoutAllDevices: string;
    today: string;
    january2024: string;
  };

  // Sterilization History
  sterilizationHistory: {
    title: string;
    history: string;
    filter: string;
    dateRange: string;
    robotFilter: string;
    statusFilter: string;
    export: string;
    details: string;
    startTime: string;
    endTime: string;
    coverage: string;
    effectiveness: string;
    robotDescription: string;
    activityHistory: string;
    bacteria: string;
    staphAureus: string;
    log: string;
    log4: string;
    operationsHistory: string;
    id: string;
    zone: string;
    duration: string;
    status: string;
    efficiency: string;
    operationDetails: string;
    operationId: string;
    resolve: string;
    close: string;
    all: string;
    completed: string;
    inProgress: string;
    failed: string;
    exportData: string;
    refresh: string;
  };

  // Obstacle Detection
  obstacleDetection: {
    title: string;
    detection: string;
    sensors: string;
    alerts: string;
    sensitivity: string;
    calibration: string;
    testMode: string;
    sensorStatus: string;
    frontSensor: string;
    rearSensor: string;
    sideSensors: string;
    operational: string;
    warning: string;
    error: string;
    allTypes: string;
    connection: string;
    battery: string;
    human: string;
    allSeverities: string;
    high: string;
    medium: string;
    low: string;
    severity: string;
    type: string;
    time: string;
    location: string;
    action: string;
    resolve: string;
    alertDetails: string;
    alertId: string;
    description: string;
    resolveAlert: string;
    resolutionNote: string;
    enterNote: string;
    markResolved: string;
    cancel: string;
    close: string;
    robotStopped: string;
    lowBattery: string;
    humanDetected: string;
    connectionLost: string;
    resolved: string;
    pending: string;
    critical: string;
    info: string;
  };

  // Modals and Forms
  modals: {
    spaceConfig: {
      title: string;
      roomType: string;
      dimensions: string;
      obstacles: string;
      specialRequirements: string;
      save: string;
      cancel: string;
    };
    sterilizationConfig: {
      title: string;
      mode: string;
      intensity: string;
      duration: string;
      schedule: string;
      start: string;
      cancel: string;
    };
  };

  // Status and States
  status: {
    online: string;
    offline: string;
    charging: string;
    working: string;
    idle: string;
    maintenance: string;
    error: string;
    connected: string;
    disconnected: string;
    active: string;
    inactive: string;
    resolved: string;
  };

  // Common
  common: {
    cancel: string;
    confirm: string;
    close: string;
    save: string;
    edit: string;
    delete: string;
    add: string;
    remove: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
    yes: string;
    no: string;
    ok: string;
    back: string;
    next: string;
    previous: string;
    finish: string;
    retry: string;
    refresh: string;
  };

  // Error Messages
  errors: {
    networkError: string;
    timeout: string;
    authRequired: string;
    accessDenied: string;
    notFound: string;
    serverError: string;
    invalidData: string;
    connectionFailed: string;
    loadingFailed: string;
    saveFailed: string;
    deleteFailed: string;
    updateFailed: string;
    exportFailed: string;
    importFailed: string;
    validationError: string;
    unknownError: string;
  };

  // Loading Messages
  loading: {
    general: string;
    robots: string;
    sessions: string;
    alerts: string;
    dashboard: string;
    saving: string;
    deleting: string;
    updating: string;
    exporting: string;
    importing: string;
    connecting: string;
  };

  // Authentication
  auth: {
    testInterface: string;
    testMode: string;
    loginForm: string;
    email: string;
    password: string;
    enterEmail: string;
    enterPassword: string;
    testLogin: string;
    testing: string;
    testAccounts: string;
    testResults: string;
    loginSuccess: string;
    invalidCredentials: string;
    tokenGenerated: string;
    userData: string;
    copyToken: string;
    tokenCopied: string;
  };
}
