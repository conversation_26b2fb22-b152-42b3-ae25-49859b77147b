// API Configuration
export { API_CONFIG, API_ENDPOINTS, HTTP_STATUS, DEFAULT_HEADERS } from './config';

// API Types
export type * from './types';

// API Client
export { api, apiClient } from './client';

// Services
export { authService } from './services/auth';
export { robotsService } from './services/robots';
export { sterilizationService } from './services/sterilization';
export { alertsService } from './services/alerts';
export { dashboardService } from './services/dashboard';

// Hooks
export * from './hooks/useApi';
export {
  useRobots,
  useRobot,
  useRobotStatus,
  useRobotsSummary,
  useRobotRealTimeData,
  useCreateRobot,
  useUpdateRobot,
  useDeleteRobot,
  useControlRobot,
  useReturnHome,
  useMaintenanceHistory,
  useScheduleMaintenance
} from './hooks/useRobots';
export {
  useSterilizationSessions,
  useSterilizationSession,
  useSterilizationHistory,
  useSterilizationStatistics,
  useActiveSessions,
  useSessionUpdates,
  useEfficiencyData,
  useBacteriaData,
  useStartSterilization as useStartSterilizationSession,
  useStopSterilization as useStopSterilizationSession,
  useUpdateSessionParameters,
  useExportSessionData,
  useSessionMonitoring
} from './hooks/useSterilization';
export * from './hooks/useAlerts';
export * from './hooks/useDashboard';

// WebSocket
export { webSocketService, useWebSocket } from './websocket';
export type { WebSocketEventType, WebSocketSubscription } from './websocket';

// Utility functions for API integration
export const apiUtils = {
  // Format error message for display
  formatError: (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.error) return error.error;
    return 'An unknown error occurred';
  },

  // Check if error is network related
  isNetworkError: (error: any): boolean => {
    const message = apiUtils.formatError(error).toLowerCase();
    return message.includes('network') || 
           message.includes('fetch') || 
           message.includes('connection') ||
           message.includes('timeout');
  },

  // Check if error is authentication related
  isAuthError: (error: any): boolean => {
    const message = apiUtils.formatError(error).toLowerCase();
    return message.includes('401') || 
           message.includes('unauthorized') || 
           message.includes('authentication');
  },

  // Format date for API requests
  formatDateForApi: (date: Date): string => {
    return date.toISOString();
  },

  // Parse date from API response
  parseDateFromApi: (dateString: string): Date => {
    return new Date(dateString);
  },

  // Create query string from object
  createQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  },

  // Download blob as file
  downloadBlob: (blob: Blob, filename: string): void => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Retry function with exponential backoff
  retry: async <T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          throw error;
        }
        
        // Don't retry on client errors (4xx)
        if (apiUtils.isAuthError(error) || 
            apiUtils.formatError(error).includes('400') ||
            apiUtils.formatError(error).includes('404')) {
          throw error;
        }
        
        // Wait before retry with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  },

  // Debounce function for API calls
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Throttle function for API calls
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
};
