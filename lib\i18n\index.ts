// lib/i18n/index.ts
import { Language, Translations, LANGUAGES } from './types';
import { en } from './translations/en';
import { fr } from './translations/fr';
import { ar } from './translations/ar';
import { es } from './translations/es';

export const translations: Record<Language, Translations> = {
  en,
  fr,
  ar,
  es
};

export const getTranslation = (language: Language): Translations => {
  return translations[language] || translations.en;
};

export const getLanguageInfo = (language: Language) => {
  return LANGUAGES.find(lang => lang.code === language) || LANGUAGES[0];
};

export * from './types';
