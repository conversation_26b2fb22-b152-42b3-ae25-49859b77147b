// lib/i18n/translations/fr.ts
import { Translations } from '../types';

export const fr: Translations = {
  login: {
    title: 'SteriBOT',
    subtitle: 'STÉRILISATEUR',
    welcome: 'Bon retour ! Veuillez vous connecter à votre compte.',
    email: 'Email',
    emailPlaceholder: 'Entrez votre adresse email',
    password: 'Mot de passe',
    passwordPlaceholder: 'Entrez votre mot de passe',
    connect: 'Se connecter',
    connecting: 'Connexion...',
    forgotPassword: 'Mot de passe oublié ?',
    emailRequired: 'L\'email est requis',
    emailInvalid: 'Veuillez entrer une adresse email valide',
    passwordRequired: 'Le mot de passe est requis',
    passwordMinLength: 'Le mot de passe doit contenir au moins 6 caractères',
    invalidCredentials: 'Email ou mot de passe invalide',
    networkError: 'Erreur réseau. Veuillez réessayer.',
    authenticationFailed: 'Échec de l\'authentification. Vérifiez vos identifiants.'
  },
  
  network: {
    title: 'Se connecter avec',
    subtitle: 'Veuillez vous connecter à un réseau WiFi ou activer vos données mobiles (4G/5G)',
    wifi: 'Se connecter avec WiFi',
    wifiDescription: 'Utiliser la connexion réseau sans fil',
    mobile: 'Se connecter avec les données mobiles',
    mobileDescription: 'Utiliser le réseau cellulaire (4G/5G)',
    connect: 'Se connecter',
    connecting: 'Connexion...'
  },
  
  connecting: {
    title: 'SteriBOT',
    subtitle: 'Connexion du robot',
    steps: {
      initializing: 'Initialisation de la connexion...',
      scanning: 'Recherche d\'appareils...',
      establishing: 'Établissement d\'une liaison sécurisée...',
      synchronizing: 'Synchronisation des données...',
      established: 'Connexion établie !'
    },
    complete: '% Terminé',
    lookingFor: 'Recherche de votre appareil intelligent',
    ensureDevice: 'Veuillez vous assurer que votre appareil SteriBOT est allumé et à portée',
    wifi: 'WiFi',
    mobileData: 'Données mobiles'
  },
  
  robot: {
    title: 'SteriBOT',
    subtitle: 'Robot de désinfection autonome',
    connected: 'Connecté',
    active: 'Actif',
    startScan: 'Démarrer le scan'
  },

  navigation: {
    home: 'Accueil',
    dashboard: 'Tableau de bord',
    robotDetails: 'Détails du robot',
    robotsList: 'Liste des robots',
    sterilizationHistory: 'Historique de stérilisation',
    obstacleDetection: 'Détection d\'obstacles',
    profile: 'Profil',
    logout: 'Déconnexion',
    authTest: 'Test Auth'
  },

  home: {
    title: 'Centre de contrôle SteriBOT',
    subtitle: 'Système de gestion des robots',
    configurationScanning: 'Configuration / Scan de l\'espace',
    robotStatus: 'État du robot',
    parameters: 'Paramètres',
    mapView: 'Vue de la carte',
    cameraView: 'Vue caméra',
    directionControls: 'Contrôles de direction',
    reset: 'Réinitialiser',
    startSterilization: 'Démarrer la stérilisation',
    spaceConfiguration: 'Configuration de l\'espace',
    sterilizationConfig: 'Configuration de stérilisation',
    position: 'Position',
    battery: 'Batterie',
    speed: 'Vitesse',
    resolution: 'Résolution',
    scanRange: 'Portée de scan',
    updateRate: 'Taux de mise à jour',
    stopScan: 'Arrêter le scan',
    saveMap: 'Sauvegarder la carte',
    mapSaved: 'Carte sauvegardée !',
    resetMap: 'Réinitialiser la carte'
  },

  dashboard: {
    title: 'Tableau de bord',
    overview: 'Vue d\'ensemble du système',
    statistics: 'Statistiques',
    activeConnections: 'Connexions actives',
    totalUsers: 'Utilisateurs totaux',
    sterilizedAreas: 'Zones stérilisées',
    efficiency: 'Efficacité',
    bacteriaDetection: 'Détection de bactéries',
    recentSessions: 'Sessions récentes',
    sessionHistory: 'Historique des sessions',
    date: 'Date',
    duration: 'Durée',
    area: 'Zone',
    status: 'État',
    completed: 'Terminé',
    inProgress: 'En cours',
    failed: 'Échoué',
    todaysConnections: 'Connexions d\'aujourd\'hui',
    todaysUsers: 'Utilisateurs d\'aujourd\'hui',
    sterilizedZones: 'Zones stérilisées',
    fromSterilizedZones: 'sur 100 zones stérilisées',
    operatingRoomA: 'Salle d\'opération A',
    surgeryRoom: 'Salle de chirurgie',
    mainCorridor: 'Couloir principal',
    operatingRoomB: 'Salle d\'opération B',
    log: 'Journal',
    others: 'Autres',
    bacteriaTypes: {
      eColi: 'E. coli',
      staphylococcus: 'Staphylocoque',
      pseudomonas: 'Pseudomonas',
      others: 'Autres'
    },
    bacteriaDetected24h: 'Bactéries détectées (24h)',
    total: 'Total',
    zone: 'Zone',
    sterilizationLogChart: 'Graphique des journaux de stérilisation',
    dateTime: 'Date/Heure',
    logStatus: 'État du journal',
    firstLog: '1er Journal',
    secondLog: '2e Journal',
    thirdLog: '3e Journal',
    fourthLog: '4e Journal',
    fifthLog: '5e Journal',
    sixthLog: '6e Journal',
    activeRobots: 'Robots Actifs',
    totalRobots: 'Total Robots',
    criticalAlerts: 'Alertes Critiques'
  },

  robotDetails: {
    title: 'Détails du robot',
    information: 'Informations du robot',
    model: 'Modèle',
    serialNumber: 'Numéro de série',
    status: 'État',
    batteryLevel: 'Niveau de batterie',
    lastMaintenance: 'Dernière maintenance',
    operatingHours: 'Heures de fonctionnement',
    specifications: 'Spécifications',
    dimensions: 'Dimensions',
    weight: 'Poids',
    maxSpeed: 'Vitesse maximale',
    uvLamps: 'Lampes UV',
    coverage: 'Zone de couverture',
    maintenance: 'Maintenance',
    schedule: 'Planification',
    nextService: 'Prochain service',
    serviceHistory: 'Historique de service',
    weekDays: {
      mon: 'Lun',
      tue: 'Mar',
      wed: 'Mer',
      thu: 'Jeu',
      fri: 'Ven',
      sat: 'Sam',
      sun: 'Dim'
    },
    months: {
      january: 'Janvier',
      february: 'Février',
      march: 'Mars',
      april: 'Avril',
      may: 'Mai',
      june: 'Juin',
      july: 'Juillet',
      august: 'Août',
      september: 'Septembre',
      october: 'Octobre',
      november: 'Novembre',
      december: 'Décembre'
    },
    scheduledTasks: 'Tâches programmées',
    operatingRoomSterilization: 'Stérilisation salle d\'opération A',
    robotMaintenanceCheck: 'Vérification maintenance robot',
    preSurgeryProtocol: 'Protocole de stérilisation pré-chirurgical',
    weeklyMaintenance: 'Routine de maintenance hebdomadaire',
    scheduled: 'Programmé',
    completed: 'Terminé',
    inProgress: 'En cours',
    high: 'Élevé',
    medium: 'Moyen',
    low: 'Faible',
    sterilization: 'Stérilisation',
    maintenanceType: 'Maintenance',
    operatingRoomA: 'Salle d\'opération A',
    robotStation: 'Station robot',
    drSmith: 'Dr. Smith',
    techTeam: 'Équipe technique',
    mainCorridorCleaning: 'Nettoyage couloir principal',
    emergencyRoomSterilization: 'Stérilisation salle d\'urgence',
    dailyCorridorSterilization: 'Stérilisation quotidienne du couloir',
    postIncidentSterilization: 'Stérilisation post-incident',
    mainCorridor: 'Couloir principal',
    emergencyRoom: 'Salle d\'urgence',
    nightShift: 'Équipe de nuit',
    drJohnson: 'Dr. Johnson',
    showCalendar: 'Afficher le calendrier',
    hideCalendar: 'Masquer le calendrier',
    edit: 'Modifier',
    day: 'Jour',
    week: 'Semaine',
    month: 'Mois',
    year: 'Année',
    addTask: 'Ajouter une tâche',
    taskTitle: 'Titre de la tâche',
    date: 'Date',
    time: 'Heure',
    duration: 'Durée (minutes)',
    priority: 'Priorité',
    roomLocation: 'Salle/Emplacement',
    assignedTo: 'Assigné à',
    notes: 'Notes',
    create: 'Créer',
    cancel: 'Annuler',
    editTask: 'Modifier la tâche',
    deleteTask: 'Supprimer la tâche',
    save: 'Enregistrer',
    minutes: 'minutes'
  },

  robotsList: {
    title: 'Liste des robots',
    addRobot: 'Ajouter un robot',
    addSpace: 'Ajouter un espace',
    robotDescription: 'Robot de désinfection autonome',
    startSterilization: 'Démarrer la stérilisation',
    name: 'Nom',
    location: 'Emplacement',
    description: 'Description',
    roomType: 'Type de pièce',
    area: 'Zone',
    addRobotTitle: 'Ajouter un nouveau robot',
    addSpaceTitle: 'Ajouter un nouvel espace',
    cancel: 'Annuler',
    add: 'Ajouter',
    addNewRobot: 'Ajouter un nouveau robot',
    robotName: 'Nom du robot',
    enterRobotName: 'Entrer le nom du robot',
    selectModel: 'Sélectionner le modèle',
    serialNumber: 'Numéro de série',
    enterSerialNumber: 'Entrer le numéro de série',
    enterLocation: 'Entrer l\'emplacement',
    enterDescription: 'Entrer la description',
    addNewSpace: 'Ajouter un nouvel espace',
    spaceName: 'Nom de l\'espace',
    enterSpaceName: 'Entrer le nom de l\'espace',
    selectRoomType: 'Sélectionner le type de pièce',
    enterArea: 'Entrer la zone',
    operatingRoom: 'Salle d\'opération',
    surgeryRoom: 'Salle de chirurgie',
    corridor: 'Couloir',
    laboratory: 'Laboratoire'
  },

  profile: {
    title: 'Profil',
    personalInfo: 'Informations personnelles',
    name: 'Nom',
    email: 'Email',
    role: 'Rôle',
    department: 'Département',
    phone: 'Téléphone',
    settings: 'Paramètres',
    notifications: 'Notifications',
    language: 'Langue',
    timezone: 'Fuseau horaire',
    theme: 'Thème',
    security: 'Sécurité',
    changePassword: 'Changer le mot de passe',
    twoFactor: 'Authentification à deux facteurs',
    loginHistory: 'Historique de connexion',
    editProfile: 'Modifier le profil',
    accountStatus: 'État du compte',
    memberSince: 'Membre depuis',
    lastLogin: 'Dernière connexion',
    accessLevel: 'Niveau d\'accès',
    administrator: 'Administrateur',
    details: 'Détails',
    lastName: 'Nom de famille',
    jobTitle: 'Titre du poste',
    doctor: 'Docteur',
    cardiology: 'Cardiologie',
    securitySettings: 'Actions rapides',
    downloadData: 'Télécharger le rapport d\'activité',
    notifications2FA: 'Paramètres de notification',
    logoutAllDevices: 'Se déconnecter',
    today: 'Aujourd\'hui',
    january2024: 'Janvier 2024'
  },

  sterilizationHistory: {
    title: 'Historique de stérilisation',
    history: 'Historique',
    filter: 'Filtrer',
    dateRange: 'Plage de dates',
    robotFilter: 'Filtre robot',
    statusFilter: 'Filtre d\'état',
    export: 'Exporter',
    details: 'Détails',
    startTime: 'Heure de début',
    endTime: 'Heure de fin',
    coverage: 'Couverture',
    effectiveness: 'Efficacité',
    robotDescription: 'Robot de désinfection autonome',
    activityHistory: 'Historique d\'activité',
    bacteria: 'Bactérie',
    staphAureus: 'Staph. aureus',
    log: 'Journal',
    log4: 'Journal 4',
    operationsHistory: 'Historique des opérations',
    id: 'ID',
    zone: 'Zone',
    duration: 'Durée',
    status: 'État',
    efficiency: 'Efficacité',
    operationDetails: 'Détails de l\'opération',
    operationId: 'ID de l\'opération',
    resolve: 'Résoudre',
    close: 'Fermer',
    all: 'Tous',
    completed: 'Terminé',
    inProgress: 'En cours',
    failed: 'Incident',
    exportData: 'Exporter les données',
    refresh: 'Actualiser'
  },

  obstacleDetection: {
    title: 'Détection d\'obstacles',
    detection: 'Système de détection',
    sensors: 'Capteurs',
    alerts: 'Alertes',
    sensitivity: 'Sensibilité',
    calibration: 'Calibrage',
    testMode: 'Mode test',
    sensorStatus: 'État des capteurs',
    frontSensor: 'Capteur avant',
    rearSensor: 'Capteur arrière',
    sideSensors: 'Capteurs latéraux',
    operational: 'Opérationnel',
    warning: 'Avertissement',
    error: 'Erreur',
    allTypes: 'Tous les types',
    connection: 'Connexion',
    battery: 'Batterie',
    human: 'Humain',
    allSeverities: 'Toutes les sévérités',
    high: 'Élevé',
    medium: 'Moyen',
    low: 'Faible',
    severity: 'Sévérité',
    type: 'Type',
    time: 'Heure',
    location: 'Emplacement',
    action: 'Action',
    resolve: 'Résoudre',
    alertDetails: 'Détails de l\'alerte',
    alertId: 'ID de l\'alerte',
    description: 'Description',
    resolveAlert: 'Résoudre l\'alerte',
    resolutionNote: 'Note de résolution',
    enterNote: 'Entrer une note de résolution',
    markResolved: 'Marquer comme résolu',
    cancel: 'Annuler',
    close: 'Fermer',
    robotStopped: 'Robot arrêté à cause d\'un obstacle',
    lowBattery: 'Avertissement de batterie faible',
    humanDetected: 'Humain détecté dans la zone de stérilisation',
    connectionLost: 'Connexion perdue avec le robot',
    resolved: 'Résolu',
    pending: 'En attente',
    critical: 'Critique',
    info: 'Info'
  },

  modals: {
    spaceConfig: {
      title: 'Configuration de l\'espace',
      roomType: 'Type de pièce',
      dimensions: 'Dimensions',
      obstacles: 'Obstacles',
      specialRequirements: 'Exigences spéciales',
      save: 'Enregistrer la configuration',
      cancel: 'Annuler'
    },
    sterilizationConfig: {
      title: 'Configuration de stérilisation',
      mode: 'Mode de stérilisation',
      intensity: 'Intensité UV',
      duration: 'Durée',
      schedule: 'Planification',
      start: 'Démarrer la stérilisation',
      cancel: 'Annuler'
    }
  },

  status: {
    online: 'En ligne',
    offline: 'Hors ligne',
    charging: 'En charge',
    working: 'En fonctionnement',
    idle: 'Inactif',
    maintenance: 'Maintenance',
    error: 'Erreur',
    connected: 'Connecté',
    disconnected: 'Déconnecté',
    active: 'Actif',
    inactive: 'Inactif',
    resolved: 'Résolu'
  },

  common: {
    cancel: 'Annuler',
    confirm: 'Confirmer',
    close: 'Fermer',
    save: 'Enregistrer',
    edit: 'Modifier',
    delete: 'Supprimer',
    add: 'Ajouter',
    remove: 'Retirer',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information',
    yes: 'Oui',
    no: 'Non',
    ok: 'OK',
    back: 'Retour',
    next: 'Suivant',
    previous: 'Précédent',
    finish: 'Terminer',
    retry: 'Réessayer',
    refresh: 'Actualiser'
  },

  errors: {
    networkError: 'Erreur réseau - veuillez vérifier votre connexion',
    timeout: 'Délai d\'attente dépassé - veuillez réessayer',
    authRequired: 'Authentification requise - veuillez vous connecter',
    accessDenied: 'Accès refusé - permissions insuffisantes',
    notFound: 'Ressource non trouvée',
    serverError: 'Erreur serveur - veuillez réessayer plus tard',
    invalidData: 'Données invalides fournies',
    connectionFailed: 'Échec de la connexion',
    loadingFailed: 'Échec du chargement des données',
    saveFailed: 'Échec de la sauvegarde',
    deleteFailed: 'Échec de la suppression',
    updateFailed: 'Échec de la mise à jour',
    exportFailed: 'Échec de l\'exportation',
    importFailed: 'Échec de l\'importation',
    validationError: 'Erreur de validation',
    unknownError: 'Une erreur inconnue s\'est produite'
  },

  loading: {
    general: 'Chargement...',
    robots: 'Chargement des robots...',
    sessions: 'Chargement des sessions...',
    alerts: 'Chargement des alertes...',
    dashboard: 'Chargement du tableau de bord...',
    saving: 'Sauvegarde...',
    deleting: 'Suppression...',
    updating: 'Mise à jour...',
    exporting: 'Exportation...',
    importing: 'Importation...',
    connecting: 'Connexion...'
  },

  auth: {
    testInterface: 'Interface de Test d\'Authentification',
    testMode: 'Mode Test',
    loginForm: 'Formulaire de Connexion',
    email: 'Email',
    password: 'Mot de passe',
    enterEmail: 'Entrez l\'adresse email',
    enterPassword: 'Entrez le mot de passe',
    testLogin: 'Tester la Connexion',
    testing: 'Test en cours...',
    testAccounts: 'Comptes de Test',
    testResults: 'Résultats du Test',
    loginSuccess: 'Authentification réussie !',
    invalidCredentials: 'Email ou mot de passe invalide',
    tokenGenerated: 'Token Généré',
    userData: 'Données Utilisateur',
    copyToken: 'Copier le Token',
    tokenCopied: 'Token Copié !'
  }
};
