// lib/i18n/translations/en.ts
import { Translations } from '../types';

export const en: Translations = {
  login: {
    title: 'SteriBOT',
    subtitle: 'STERILISER',
    welcome: 'Welcome back! Please sign in to your account.',
    email: 'Email',
    emailPlaceholder: 'Enter your email address',
    password: 'Password',
    passwordPlaceholder: 'Enter your password',
    connect: 'Connect',
    connecting: 'Connecting...',
    forgotPassword: 'Forgot Password?',
    emailRequired: 'Email is required',
    emailInvalid: 'Please enter a valid email address',
    passwordRequired: 'Password is required',
    passwordMinLength: 'Password must be at least 6 characters',
    invalidCredentials: 'Invalid email or password',
    networkError: 'Network error occurred. Please try again.',
    authenticationFailed: 'Authentication failed. Please check your credentials.'
  },
  
  network: {
    title: 'Connect with',
    subtitle: 'Please connect to a WiFi network or enable your mobile data (4G/5G)',
    wifi: 'Connect with WiFi',
    wifiDescription: 'Use wireless network connection',
    mobile: 'Connect with Mobile Data',
    mobileDescription: 'Use cellular network (4G/5G)',
    connect: 'Connect',
    connecting: 'Connecting...'
  },
  
  connecting: {
    title: 'SteriBOT',
    subtitle: 'Connecting robot',
    steps: {
      initializing: 'Initializing connection...',
      scanning: 'Scanning for devices...',
      establishing: 'Establishing secure link...',
      synchronizing: 'Synchronizing data...',
      established: 'Connection established!'
    },
    complete: '% Complete',
    lookingFor: 'Looking for your smart device',
    ensureDevice: 'Please ensure your SteriBOT device is powered on and within range',
    wifi: 'WiFi',
    mobileData: 'Mobile Data'
  },
  
  robot: {
    title: 'SteriBOT',
    subtitle: 'Autonomous disinfection robot',
    connected: 'Connected',
    active: 'Active',
    startScan: 'Start Scan'
  },

  navigation: {
    home: 'Home',
    dashboard: 'Dashboard',
    robotDetails: 'Robot Details',
    robotsList: 'Robots List',
    sterilizationHistory: 'Sterilization History',
    obstacleDetection: 'Obstacle Detection',
    profile: 'Profile',
    logout: 'Logout',
    authTest: 'Auth Test'
  },

  home: {
    title: 'SteriBOT Control Center',
    subtitle: 'Robot Management System',
    configurationScanning: 'Configuration / Scanning space',
    robotStatus: 'Robot Status',
    parameters: 'Parameters',
    mapView: 'Map View',
    cameraView: 'Camera View',
    directionControls: 'Direction Controls',
    reset: 'Reset',
    startSterilization: 'Start Sterilization',
    spaceConfiguration: 'Space Configuration',
    sterilizationConfig: 'Sterilization Configuration',
    position: 'Position',
    battery: 'Battery',
    speed: 'Speed',
    resolution: 'Resolution',
    scanRange: 'Scan Range',
    updateRate: 'Update Rate',
    stopScan: 'Stop Scan',
    saveMap: 'Save the Map',
    mapSaved: 'Map Saved!',
    resetMap: 'Reset Map'
  },

  dashboard: {
    title: 'Dashboard',
    overview: 'System Overview',
    statistics: 'Statistics',
    activeConnections: 'Active Connections',
    totalUsers: 'Total Users',
    sterilizedAreas: 'Sterilized Areas',
    efficiency: 'Efficiency',
    bacteriaDetection: 'Bacteria Detection',
    recentSessions: 'Recent Sessions',
    sessionHistory: 'Session History',
    date: 'Date',
    duration: 'Duration',
    area: 'Area',
    status: 'Status',
    completed: 'Completed',
    inProgress: 'In Progress',
    failed: 'Failed',
    todaysConnections: 'Today\'s Connections',
    todaysUsers: 'Today\'s Users',
    sterilizedZones: 'Sterilized Zones',
    fromSterilizedZones: 'from 100 sterilized zones',
    operatingRoomA: 'Operating Room A',
    surgeryRoom: 'Surgery Room',
    mainCorridor: 'Main Corridor',
    operatingRoomB: 'Operating Room B',
    log: 'Log',
    others: 'Others',
    bacteriaTypes: {
      eColi: 'E. coli',
      staphylococcus: 'Staphylococcus',
      pseudomonas: 'Pseudomonas',
      others: 'Others'
    },
    bacteriaDetected24h: 'Bacteria Detected (24h)',
    total: 'Total',
    zone: 'Zone',
    sterilizationLogChart: 'Sterilization log chart',
    dateTime: 'Date/Time',
    logStatus: 'Log Status',
    firstLog: '1st Log',
    secondLog: '2nd Log',
    thirdLog: '3rd Log',
    fourthLog: '4th Log',
    fifthLog: '5th Log',
    sixthLog: '6th Log',
    activeRobots: 'Active Robots',
    totalRobots: 'Total Robots',
    criticalAlerts: 'Critical Alerts'
  },

  robotDetails: {
    title: 'Robot Details',
    information: 'Robot Information',
    model: 'Model',
    serialNumber: 'Serial Number',
    status: 'Status',
    batteryLevel: 'Battery Level',
    lastMaintenance: 'Last Maintenance',
    operatingHours: 'Operating Hours',
    specifications: 'Specifications',
    dimensions: 'Dimensions',
    weight: 'Weight',
    maxSpeed: 'Max Speed',
    uvLamps: 'UV Lamps',
    coverage: 'Coverage Area',
    maintenance: 'Maintenance',
    schedule: 'Schedule',
    nextService: 'Next Service',
    serviceHistory: 'Service History',
    weekDays: {
      mon: 'Mon',
      tue: 'Tue',
      wed: 'Wed',
      thu: 'Thu',
      fri: 'Fri',
      sat: 'Sat',
      sun: 'Sun'
    },
    months: {
      january: 'January',
      february: 'February',
      march: 'March',
      april: 'April',
      may: 'May',
      june: 'June',
      july: 'July',
      august: 'August',
      september: 'September',
      october: 'October',
      november: 'November',
      december: 'December'
    },
    scheduledTasks: 'Scheduled Tasks',
    operatingRoomSterilization: 'Operating Room A Sterilization',
    robotMaintenanceCheck: 'Robot Maintenance Check',
    preSurgeryProtocol: 'Pre-surgery sterilization protocol',
    weeklyMaintenance: 'Weekly maintenance routine',
    scheduled: 'Scheduled',
    completed: 'Completed',
    inProgress: 'In Progress',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    sterilization: 'Sterilization',
    maintenanceType: 'Maintenance',
    operatingRoomA: 'Operating Room A',
    robotStation: 'Robot Station',
    drSmith: 'Dr. Smith',
    techTeam: 'Tech Team',
    mainCorridorCleaning: 'Main Corridor Cleaning',
    emergencyRoomSterilization: 'Emergency Room Sterilization',
    dailyCorridorSterilization: 'Daily corridor sterilization',
    postIncidentSterilization: 'Post-incident sterilization',
    mainCorridor: 'Main Corridor',
    emergencyRoom: 'Emergency Room',
    nightShift: 'Night Shift',
    drJohnson: 'Dr. Johnson',
    showCalendar: 'Show Calendar',
    hideCalendar: 'Hide Calendar',
    edit: 'Edit',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    year: 'Year',
    addTask: 'Add Task',
    taskTitle: 'Task Title',
    date: 'Date',
    time: 'Time',
    duration: 'Duration (minutes)',
    priority: 'Priority',
    roomLocation: 'Room/Location',
    assignedTo: 'Assigned To',
    notes: 'Notes',
    create: 'Create',
    cancel: 'Cancel',
    editTask: 'Edit Task',
    deleteTask: 'Delete Task',
    save: 'Save',
    minutes: 'minutes'
  },

  robotsList: {
    title: 'Robots List',
    addRobot: 'Add Robot',
    addSpace: 'Add Space',
    robotDescription: 'Autonomous disinfection robot',
    startSterilization: 'Start sterilization',
    name: 'Name',
    location: 'Location',
    description: 'Description',
    roomType: 'Room Type',
    area: 'Area',
    addRobotTitle: 'Add New Robot',
    addSpaceTitle: 'Add New Space',
    cancel: 'Cancel',
    add: 'Add',
    addNewRobot: 'Add new robot',
    robotName: 'Robot Name',
    enterRobotName: 'Enter robot name',
    selectModel: 'Select model',
    serialNumber: 'Serial Number',
    enterSerialNumber: 'Enter serial number',
    enterLocation: 'Enter location',
    enterDescription: 'Enter description',
    addNewSpace: 'Add new space',
    spaceName: 'Space Name',
    enterSpaceName: 'Enter space name',
    selectRoomType: 'Select room type',
    enterArea: 'Enter area',
    operatingRoom: 'Operating Room',
    surgeryRoom: 'Surgery Room',
    corridor: 'Corridor',
    laboratory: 'Laboratory'
  },

  profile: {
    title: 'Profile',
    personalInfo: 'Personal Information',
    name: 'Name',
    email: 'Email',
    role: 'Role',
    department: 'Department',
    phone: 'Phone',
    settings: 'Settings',
    notifications: 'Notifications',
    language: 'Language',
    timezone: 'Timezone',
    theme: 'Theme',
    security: 'Security',
    changePassword: 'Change Password',
    twoFactor: 'Two-Factor Authentication',
    loginHistory: 'Login History',
    editProfile: 'Edit Profile',
    accountStatus: 'Account Status',
    memberSince: 'Member Since',
    lastLogin: 'Last Login',
    accessLevel: 'Access Level',
    administrator: 'Administrator',
    details: 'Details',
    lastName: 'Last Name',
    jobTitle: 'Job Title',
    doctor: 'Doctor',
    cardiology: 'Cardiology',
    securitySettings: 'Security Settings',
    downloadData: 'Download Data',
    notifications2FA: 'Notifications & 2FA',
    logoutAllDevices: 'Logout All Devices',
    today: 'Today',
    january2024: 'January 2024'
  },

  sterilizationHistory: {
    title: 'Sterilization History',
    history: 'History',
    filter: 'Filter',
    dateRange: 'Date Range',
    robotFilter: 'Robot Filter',
    statusFilter: 'Status Filter',
    export: 'Export',
    details: 'Details',
    startTime: 'Start Time',
    endTime: 'End Time',
    coverage: 'Coverage',
    effectiveness: 'Effectiveness',
    robotDescription: 'Autonomous disinfection robot',
    activityHistory: 'Activity History',
    bacteria: 'Bacteria',
    staphAureus: 'Staph. aureus',
    log: 'Log',
    log4: 'Log 4',
    operationsHistory: 'Operations History',
    id: 'ID',
    zone: 'Zone',
    duration: 'Duration',
    status: 'Status',
    efficiency: 'Efficiency',
    operationDetails: 'Operation Details',
    operationId: 'Operation ID',
    resolve: 'Resolve',
    close: 'Close',
    all: 'All',
    completed: 'Completed',
    inProgress: 'In Progress',
    failed: 'Failed',
    exportData: 'Export Data',
    refresh: 'Refresh'
  },

  obstacleDetection: {
    title: 'Obstacle Detection',
    detection: 'Detection System',
    sensors: 'Sensors',
    alerts: 'Alerts',
    sensitivity: 'Sensitivity',
    calibration: 'Calibration',
    testMode: 'Test Mode',
    sensorStatus: 'Sensor Status',
    frontSensor: 'Front Sensor',
    rearSensor: 'Rear Sensor',
    sideSensors: 'Side Sensors',
    operational: 'Operational',
    warning: 'Warning',
    error: 'Error',
    allTypes: 'All Types',
    connection: 'Connection',
    battery: 'Battery',
    human: 'Human',
    allSeverities: 'All Severities',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    severity: 'Severity',
    type: 'Type',
    time: 'Time',
    location: 'Location',
    action: 'Action',
    resolve: 'Resolve',
    alertDetails: 'Alert Details',
    alertId: 'Alert ID',
    description: 'Description',
    resolveAlert: 'Resolve Alert',
    resolutionNote: 'Resolution Note',
    enterNote: 'Enter resolution note',
    markResolved: 'Mark as Resolved',
    cancel: 'Cancel',
    close: 'Close',
    robotStopped: 'Robot stopped due to obstacle',
    lowBattery: 'Low battery warning',
    humanDetected: 'Human detected in sterilization zone',
    connectionLost: 'Connection lost with robot',
    resolved: 'Resolved',
    pending: 'Pending',
    critical: 'Critical',
    info: 'Info'
  },

  modals: {
    spaceConfig: {
      title: 'Space Configuration',
      roomType: 'Room Type',
      dimensions: 'Dimensions',
      obstacles: 'Obstacles',
      specialRequirements: 'Special Requirements',
      save: 'Save Configuration',
      cancel: 'Cancel'
    },
    sterilizationConfig: {
      title: 'Sterilization Configuration',
      mode: 'Sterilization Mode',
      intensity: 'UV Intensity',
      duration: 'Duration',
      schedule: 'Schedule',
      start: 'Start Sterilization',
      cancel: 'Cancel'
    }
  },

  status: {
    online: 'Online',
    offline: 'Offline',
    charging: 'Charging',
    working: 'Working',
    idle: 'Idle',
    maintenance: 'Maintenance',
    error: 'Error',
    connected: 'Connected',
    disconnected: 'Disconnected',
    active: 'Active',
    inactive: 'Inactive',
    resolved: 'Resolved'
  },

  common: {
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    remove: 'Remove',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    retry: 'Retry',
    refresh: 'Refresh',
    apiStatus: 'API Status',
    online: 'Online',
    offline: 'Offline',
    endpoint: 'Endpoint',
    responseTime: 'Response Time',
    lastChecked: 'Last Checked',
    note: 'Note',
    apiOfflineMessage: 'Make sure the backend API is running on'
  },

  errors: {
    networkError: 'Network error - please check your connection',
    timeout: 'Request timeout - please try again',
    authRequired: 'Authentication required - please log in',
    accessDenied: 'Access denied - insufficient permissions',
    notFound: 'Resource not found',
    serverError: 'Server error - please try again later',
    invalidData: 'Invalid data provided',
    connectionFailed: 'Connection failed',
    loadingFailed: 'Failed to load data',
    saveFailed: 'Failed to save changes',
    deleteFailed: 'Failed to delete item',
    updateFailed: 'Failed to update item',
    exportFailed: 'Failed to export data',
    importFailed: 'Failed to import data',
    validationError: 'Validation error',
    unknownError: 'An unknown error occurred'
  },

  loading: {
    general: 'Loading...',
    robots: 'Loading robots...',
    sessions: 'Loading sessions...',
    alerts: 'Loading alerts...',
    dashboard: 'Loading dashboard...',
    saving: 'Saving...',
    deleting: 'Deleting...',
    updating: 'Updating...',
    exporting: 'Exporting...',
    importing: 'Importing...',
    connecting: 'Connecting...'
  },

  auth: {
    testInterface: 'Authentication Test Interface',
    testMode: 'Test Mode',
    loginForm: 'Login Form',
    email: 'Email',
    password: 'Password',
    enterEmail: 'Enter email address',
    enterPassword: 'Enter password',
    testLogin: 'Test Login',
    testing: 'Testing...',
    testAccounts: 'Test Accounts',
    testResults: 'Test Results',
    loginSuccess: 'Authentication successful!',
    invalidCredentials: 'Invalid email or password',
    tokenGenerated: 'Generated Token',
    userData: 'User Data',
    copyToken: 'Copy Token',
    tokenCopied: 'Token Copied!'
  }
};
