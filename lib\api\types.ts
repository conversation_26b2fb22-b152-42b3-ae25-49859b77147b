// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface ApiError {
  message: string;
  error: string;
  statusCode: number;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  role?: 'user' | 'admin';
  language?: string;
}

export interface LoginResponse {
  message: string;
  customToken: string;
  instructions?: string;
  user: User;
}

export interface User {
  userId: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  language?: string;
  createdAt: string;
  lastLogin?: string;
}

// Robot Types
export interface Robot {
  id: string;
  name: string;
  model: string;
  serialNumber: string;
  status: RobotStatus;
  batteryLevel: number;
  position: {
    x: number;
    y: number;
    z: number;
  };
  lastMaintenance: string;
  operatingHours: number;
  specifications: RobotSpecifications;
  createdAt: string;
  updatedAt: string;
}

export interface RobotStatus {
  online: boolean;
  operational: boolean;
  currentTask: string | null;
  speed: number;
  temperature: number;
  uvLampStatus: boolean;
  errorCode: string | null;
  lastUpdate: string;
}

export interface RobotSpecifications {
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  weight: number;
  maxSpeed: number;
  uvLamps: number;
  coverageArea: number;
  batteryCapacity: number;
}

export interface RobotControlRequest {
  action: 'start' | 'stop' | 'pause' | 'resume' | 'return_home';
  parameters?: Record<string, any>;
}

// Sterilization Types
export interface SterilizationSession {
  id: string;
  robotId: string;
  robotName: string;
  zone: string;
  startTime: string;
  endTime: string | null;
  duration: number;
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  efficiency: number;
  coverage: number;
  bacteriaDetected: BacteriaDetection[];
  parameters: SterilizationParameters;
  createdAt: string;
}

export interface BacteriaDetection {
  type: string;
  count: number;
  eliminated: number;
  location: {
    x: number;
    y: number;
  };
  timestamp: string;
}

export interface SterilizationParameters {
  uvIntensity: number;
  duration: number;
  pattern: string;
  speed: number;
}

export interface StartSterilizationRequest {
  robotId: string;
  zone: string;
  parameters: SterilizationParameters;
  scheduledTime?: string;
}

// Alert Types
export interface Alert {
  id: string;
  type: 'connection' | 'battery' | 'human' | 'obstacle' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  description: string;
  robotId?: string;
  location?: string;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  resolutionNote?: string;
}

export interface ResolveAlertRequest {
  note: string;
  resolvedBy: string;
}

// Dashboard Types
export interface DashboardOverview {
  totalRobots: number;
  activeRobots: number;
  completedSessions: number;
  activeSessions: number;
  totalAlerts: number;
  criticalAlerts: number;
  systemHealth: number;
  lastUpdate: string;
}

export interface DashboardStatistics {
  sessionsToday: number;
  sessionsThisWeek: number;
  sessionsThisMonth: number;
  averageEfficiency: number;
  totalCoverage: number;
  bacteriaEliminated: number;
  uptime: number;
}

// Settings Types
export interface NetworkSettings {
  wifi: {
    ssid: string;
    connected: boolean;
    signalStrength: number;
  };
  ethernet: {
    connected: boolean;
    ipAddress: string;
    gateway: string;
  };
}

export interface SystemSettings {
  language: string;
  timezone: string;
  theme: string;
  notifications: boolean;
  autoUpdates: boolean;
}

// Request/Response Filters
export interface ListFilters {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'robot_status' | 'alert' | 'session_update' | 'system_status';
  data: any;
  timestamp: string;
}
