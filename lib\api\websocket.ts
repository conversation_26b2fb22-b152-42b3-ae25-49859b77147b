import { API_CONFIG } from './config';
import { WebSocketMessage } from './types';

export type WebSocketEventType = 'robot_status' | 'alert' | 'session_update' | 'system_status';

export interface WebSocketSubscription {
  id: string;
  type: WebSocketEventType;
  callback: (data: any) => void;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private subscriptions: Map<string, WebSocketSubscription> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private authToken: string | null = null;

  constructor() {
    // Load auth token from localStorage
    if (typeof window !== 'undefined') {
      this.authToken = localStorage.getItem('auth_token');
    }
  }

  // Connect to WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        return;
      }

      this.isConnecting = true;

      try {
        const wsUrl = `${API_CONFIG.BASE_URL.replace('http', 'ws')}/ws`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // Send authentication if token is available
          if (this.authToken) {
            this.send({
              type: 'auth',
              data: { token: this.authToken },
              timestamp: new Date().toISOString(),
            });
          }
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;
          
          // Attempt to reconnect if not a normal closure
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.subscriptions.clear();
  }

  // Send message to WebSocket server
  private send(message: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  // Handle incoming WebSocket messages
  private handleMessage(message: WebSocketMessage): void {
    // Notify all subscribers for this message type
    this.subscriptions.forEach((subscription) => {
      if (subscription.type === message.type) {
        try {
          subscription.callback(message.data);
        } catch (error) {
          console.error('Error in WebSocket subscription callback:', error);
        }
      }
    });
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      this.connect().catch((error) => {
        console.error('WebSocket reconnect failed:', error);
      });
    }, delay);
  }

  // Subscribe to WebSocket events
  subscribe(type: WebSocketEventType, callback: (data: any) => void): string {
    const id = Math.random().toString(36).substr(2, 9);
    const subscription: WebSocketSubscription = {
      id,
      type,
      callback,
    };
    
    this.subscriptions.set(id, subscription);
    
    // Auto-connect if not already connected
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.connect().catch((error) => {
        console.error('Failed to connect WebSocket for subscription:', error);
      });
    }
    
    return id;
  }

  // Unsubscribe from WebSocket events
  unsubscribe(subscriptionId: string): void {
    this.subscriptions.delete(subscriptionId);
    
    // Disconnect if no more subscriptions
    if (this.subscriptions.size === 0) {
      this.disconnect();
    }
  }

  // Set authentication token
  setAuthToken(token: string | null): void {
    this.authToken = token;
    
    // Send auth message if connected
    if (this.ws?.readyState === WebSocket.OPEN && token) {
      this.send({
        type: 'auth',
        data: { token },
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Get connection status
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // Get number of active subscriptions
  getSubscriptionCount(): number {
    return this.subscriptions.size;
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();

// React hook for WebSocket subscriptions
export function useWebSocket(
  type: WebSocketEventType,
  callback: (data: any) => void,
  enabled: boolean = true
): {
  isConnected: boolean;
  subscriptionCount: number;
} {
  const [isConnected, setIsConnected] = React.useState(false);
  const [subscriptionCount, setSubscriptionCount] = React.useState(0);

  React.useEffect(() => {
    if (!enabled) return;

    const subscriptionId = webSocketService.subscribe(type, callback);

    // Update connection status
    const updateStatus = () => {
      setIsConnected(webSocketService.isConnected());
      setSubscriptionCount(webSocketService.getSubscriptionCount());
    };

    updateStatus();
    const interval = setInterval(updateStatus, 1000);

    return () => {
      webSocketService.unsubscribe(subscriptionId);
      clearInterval(interval);
    };
  }, [type, callback, enabled]);

  return {
    isConnected,
    subscriptionCount,
  };
}

// Import React for the hook
import React from 'react';
