// components/ConnectingInterface.tsx
"use client"

import { useEffect, useState } from 'react';
import { Wifi, ArrowR<PERSON>, Zap } from 'lucide-react';
import { useTranslation } from '@/lib/i18n/context';

interface ConnectingInterfaceProps {
  connectionType: 'wifi' | 'mobile';
  onConnectionComplete: () => void;
}

export function ConnectingInterface({ connectionType, onConnectionComplete }: ConnectingInterfaceProps) {
  const t = useTranslation();
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    t.connecting.steps.initializing,
    t.connecting.steps.scanning,
    t.connecting.steps.establishing,
    t.connecting.steps.synchronizing,
    t.connecting.steps.established
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(() => onConnectionComplete(), 1000);
          return 100;
        }
        return prev + 2;
      });
    }, 100);

    return () => clearInterval(timer);
  }, [onConnectionComplete]);

  useEffect(() => {
    const stepTimer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= steps.length - 1) {
          clearInterval(stepTimer);
          return prev;
        }
        return prev + 1;
      });
    }, 1000);

    return () => clearInterval(stepTimer);
  }, [steps.length]);

  return (
    <div className="min-h-screen connecting-container-inverted flex flex-col">
      {/* Blurred Background */}
      <div className="absolute inset-0 connecting-background"></div>
      
      {/* Header Section */}
      <header className="relative z-10 p-6 flex items-center gap-4">
        <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg">
          <img 
            src="/images/image.png" 
            alt="SteriBOT Logo" 
            className="w-10 h-10 object-cover rounded-full"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
              (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
            }}
          />
          <div 
            className="w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden"
          >
            S
          </div>
        </div>
        <div>
          <h1 className="text-2xl font-bold text-teal-800">{t.connecting.title}</h1>
          <p className="text-teal-600 text-sm">{t.connecting.subtitle}</p>
        </div>
      </header>

      {/* Center Animation Section with 3D Card */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-8">
        <div className="connecting-card-3d w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-teal-200/30 shadow-3d">
          {/* 3D Depth Layers */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
          <div className="absolute inset-1 rounded-2xl bg-gradient-to-tl from-teal-100/20 to-transparent pointer-events-none"></div>
          
          <div className="relative z-10">
            {/* Connection Flow Animation */}
            <div className="flex items-center justify-center gap-16 mb-16">
              {/* WiFi/Mobile Icon */}
              <div className="relative">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center wifi-pulse border-2 border-teal-400/30 shadow-lg">
                  {connectionType === 'wifi' ? (
                    <Wifi className="w-12 h-12 text-white" />
                  ) : (
                    <Zap className="w-12 h-12 text-white" />
                  )}
                </div>
                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                  <span className="text-teal-800 text-sm font-medium">
                    {connectionType === 'wifi' ? t.connecting.wifi : t.connecting.mobileData}
                  </span>
                </div>
              </div>

              {/* Animated Arrows */}
              <div className="flex items-center gap-4 flow-arrows">
                <ArrowRight className="w-8 h-8 text-teal-600" />
                <ArrowRight className="w-8 h-8 text-teal-700" />
                <ArrowRight className="w-8 h-8 text-teal-800" />
              </div>

              {/* Robot/Sterilizer */}
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-teal-600/80 to-teal-800/80 backdrop-blur-sm flex items-center justify-center robot-waves border-2 border-teal-400/30 shadow-lg">
                  <div className="w-20 h-20 rounded-lg bg-gradient-to-br from-white/20 to-white/5 flex items-center justify-center">
                    <img 
                      src="/images/image.png" 
                      alt="SteriBOT Device" 
                      className="w-16 h-16 object-cover rounded-lg"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
                      }}
                    />
                    <div 
                      className="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-lg items-center justify-center text-white font-bold text-2xl hidden"
                    >
                      S
                    </div>
                  </div>
                </div>
                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                  <span className="text-teal-800 text-sm font-medium">SteriBOT</span>
                </div>
              </div>
            </div>

            {/* Progress Section */}
            <div className="text-center mb-8">
              <div className="w-full max-w-md mx-auto mb-6">
                <div className="w-full bg-teal-200/30 rounded-full h-3 backdrop-blur-sm border border-teal-300/40">
                  <div 
                    className="h-full rounded-full transition-all duration-300 ease-out"
                    style={{ 
                      width: `${progress}%`,
                      background: 'linear-gradient(90deg, #14b8a6, #0C6980)'
                    }}
                  ></div>
                </div>
                <div className="mt-2 text-teal-800 text-sm font-medium">
                  {progress}{t.connecting.complete}
                </div>
              </div>

              {/* Current Step */}
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-teal-800 mb-2">
                  {steps[currentStep]}
                </h2>
                <div className="flex justify-center gap-2">
                  {steps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index <= currentStep 
                          ? 'bg-teal-600 scale-110' 
                          : 'bg-teal-300/50'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Bottom Text Section */}
            <div className="text-center">
              <h3 className="text-2xl font-bold gradient-text mb-2">
                {t.connecting.lookingFor}
              </h3>
              <p className="text-teal-700 text-lg">
                {t.connecting.ensureDevice}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Particles Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-teal-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.1;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 0.3;
          }
        }
      `}</style>
    </div>
  );
}
