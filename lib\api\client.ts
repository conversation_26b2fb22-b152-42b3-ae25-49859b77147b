import { API_CONFIG } from './config';

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// API Error class
export class ApiClientError extends Error {
  public status?: number;
  public code?: string;
  public details?: any;

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message);
    this.name = 'ApiClientError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

// API Response interface
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

// Enhanced API Client
export class ApiClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
    this.retryDelay = API_CONFIG.RETRY_DELAY;
    
    // Load auth token from localStorage
    if (typeof window !== 'undefined') {
      this.authToken = localStorage.getItem('auth_token');
    }
  }

  // Set authentication token
  setAuthToken(token: string | null): void {
    this.authToken = token;
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('auth_token', token);
      } else {
        localStorage.removeItem('auth_token');
      }
    }
  }

  // Get authentication token
  getAuthToken(): string | null {
    return this.authToken;
  }

  // Get default headers
  private getHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return { ...headers, ...customHeaders };
  }

  // Create timeout controller
  private createTimeoutController(): AbortController {
    const controller = new AbortController();
    setTimeout(() => controller.abort(), this.timeout);
    return controller;
  }

  // Enhanced error handling
  private handleError(error: any, url: string, response?: Response): never {
    console.error(`API Error for ${url}:`, error);

    // Handle AbortError (timeout)
    if (error.name === 'AbortError') {
      throw new ApiClientError(
        'Request timeout - the server took too long to respond',
        0,
        'TIMEOUT'
      );
    }

    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiClientError(
        `Network error - cannot connect to the API server. Please check if the backend is running.`,
        0,
        'NETWORK_ERROR',
        { originalError: error.message, url }
      );
    }

    // Handle HTTP errors with response
    if (response) {
      throw new ApiClientError(
        error.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        'HTTP_ERROR',
        { url, status: response.status, statusText: response.statusText }
      );
    }

    // Handle other errors
    throw new ApiClientError(
      error.message || 'An unexpected error occurred',
      0,
      'UNKNOWN_ERROR',
      { originalError: error }
    );
  }

  // Sleep utility for retry delays
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Should retry logic
  private shouldRetry(error: ApiClientError, attempt: number): boolean {
    if (attempt >= this.retryAttempts) return false;
    
    // Don't retry client errors (4xx)
    if (error.status && error.status >= 400 && error.status < 500) {
      return false;
    }
    
    // Retry network errors and server errors (5xx)
    return error.code === 'NETWORK_ERROR' ||
           error.code === 'TIMEOUT' ||
           (error.status !== undefined && error.status >= 500);
  }

  // Main request method with retry logic
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    attempt: number = 1
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const controller = this.createTimeoutController();
    
    try {
      console.log(`API Request [${attempt}/${this.retryAttempts}]: ${options.method || 'GET'} ${url}`);
      
      const response = await fetch(url, {
        ...options,
        headers: this.getHeaders(options.headers as Record<string, string>),
        signal: controller.signal,
      });

      console.log(`API Response: ${response.status} ${response.statusText}`);

      // Handle authentication errors
      if (response.status === HTTP_STATUS.UNAUTHORIZED) {
        this.setAuthToken(null);
        throw new ApiClientError('Authentication required', response.status, 'UNAUTHORIZED');
      }

      // Parse response based on content type
      let data: T;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text() as unknown as T;
      }

      // Handle HTTP errors
      if (!response.ok) {
        const errorMessage = (data as any)?.message || 
                           (data as any)?.error || 
                           `HTTP ${response.status}: ${response.statusText}`;
        throw new ApiClientError(errorMessage, response.status, 'HTTP_ERROR', data);
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };

    } catch (error: any) {
      const apiError = error instanceof ApiClientError ? error : 
                      new ApiClientError(error.message, 0, 'UNKNOWN_ERROR');

      // Retry logic
      if (this.shouldRetry(apiError, attempt)) {
        console.log(`Retrying request in ${this.retryDelay}ms... (attempt ${attempt + 1}/${this.retryAttempts})`);
        await this.sleep(this.retryDelay * attempt); // Exponential backoff
        return this.makeRequest<T>(endpoint, options, attempt + 1);
      }

      this.handleError(apiError, url);
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET', headers });
  }

  async post<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async put<T>(endpoint: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE', headers });
  }


}

// Create and export singleton instance
export const apiClient = new ApiClient();

// Export convenience methods
export const api = {
  get: <T>(endpoint: string, headers?: Record<string, string>) => apiClient.get<T>(endpoint, headers),
  post: <T>(endpoint: string, data?: any, headers?: Record<string, string>) => apiClient.post<T>(endpoint, data, headers),
  put: <T>(endpoint: string, data?: any, headers?: Record<string, string>) => apiClient.put<T>(endpoint, data, headers),
  delete: <T>(endpoint: string, headers?: Record<string, string>) => apiClient.delete<T>(endpoint, headers),
  setAuthToken: (token: string | null) => apiClient.setAuthToken(token),
  getAuthToken: () => apiClient.getAuthToken(),
};
