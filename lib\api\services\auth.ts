import { api, ApiClientError } from '../client';
import { API_ENDPOINTS } from '../config';
import { LoginRequest, LoginResponse } from '../types';

export class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      console.log('🔐 Attempting login with:', { email: credentials.email });

      const response = await api.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);

      console.log('✅ Login successful:', {
        status: response.status,
        hasToken: !!response.data.customToken
      });

      // Set auth token after successful login
      if (response.data.customToken) {
        api.setAuthToken(response.data.customToken);
        console.log('🎫 Auth token stored');
      }

      return response.data;
    } catch (error: any) {
      console.error('❌ Login failed:', error);

      if (error instanceof ApiClientError) {
        // Handle specific API errors
        switch (error.status) {
          case 401:
            throw new Error('Invalid email or password');
          case 400:
            throw new Error('Invalid request data. Please check your email and password.');
          case 429:
            throw new Error('Too many login attempts. Please try again later.');
          case 500:
            throw new Error('Server error. Please try again later.');
          default:
            throw new Error(error.message || 'Login failed. Please try again.');
        }
      }

      // Handle network errors
      if (error.code === 'NETWORK_ERROR') {
        throw new Error('Cannot connect to the server. Please check your internet connection and try again.');
      }

      if (error.code === 'TIMEOUT') {
        throw new Error('Request timeout. The server is taking too long to respond.');
      }

      throw new Error(error.message || 'An unexpected error occurred during login.');
    }
  }

  // Logout user (only clear local token - no backend call)
  async logout(): Promise<void> {
    console.log('🚪 Logging out (clearing local token only)...');
    // Only clear local token - no backend call
    api.setAuthToken(null);
    console.log('✅ Local token cleared');
  }







  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = api.getAuthToken();
    return !!token;
  }

  // Get current auth token
  getAuthToken(): string | null {
    return api.getAuthToken();
  }

  // Set auth token manually (for testing)
  setAuthToken(token: string | null): void {
    api.setAuthToken(token);
  }


}

export const authService = new AuthService();
