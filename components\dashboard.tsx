"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Shield, Users, Target, Activity, AlertTriangle, RefreshCw } from "lucide-react"
import { useTranslation } from "@/lib/i18n/context"
import { Button } from "@/components/ui/button"

// Static dashboard - no API calls, only static data
export function Dashboard() {
  const { t } = useTranslation()

  // Static data - no API calls
  const staticData = {
    totalRobots: 3,
    activeRobots: 1,
    totalTasks: 3,
    completedTasks: 1,
    criticalAlerts: 1,
    recentSessions: [
      {
        date: "15 Jan 2024, 14:30",
        zone: "Operating Room 1",
        duration: "45 min",
        status: "Completed"
      },
      {
        date: "15 Jan 2024, 13:15",
        zone: "Patient Room 101",
        duration: "30 min",
        status: "Completed"
      },
      {
        date: "15 Jan 2024, 12:00",
        zone: "ICU Ward",
        duration: "In progress",
        status: "Active"
      }
    ]
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t.dashboard.title}</h1>
          <p className="text-muted-foreground">{t.dashboard.subtitle}</p>
        </div>
        <Button variant="outline" size="sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          {t.common.refresh}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t.dashboard.totalRobots}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staticData.totalRobots}</div>
            <p className="text-xs text-muted-foreground">
              {staticData.activeRobots} {t.dashboard.active}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t.dashboard.activeSessions}</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staticData.activeRobots}</div>
            <p className="text-xs text-muted-foreground">
              {t.dashboard.inProgress}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t.dashboard.completedTasks}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staticData.completedTasks}</div>
            <p className="text-xs text-muted-foreground">
              {t.dashboard.today}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t.dashboard.systemHealth}</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">98%</div>
            <p className="text-xs text-muted-foreground">
              {t.dashboard.optimal}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>{t.dashboard.recentSessions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {staticData.recentSessions.map((session, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{session.zone}</p>
                    <p className="text-xs text-muted-foreground">{session.date}</p>
                  </div>
                  <div className="text-right space-y-1">
                    <p className="text-sm">{session.duration}</p>
                    <Badge variant={session.status === "Completed" ? "default" : "secondary"}>
                      {session.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              {t.dashboard.alerts}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg border-red-200 bg-red-50">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-red-800">Low Battery Alert</p>
                  <p className="text-xs text-red-600">SteriBOT-002 battery below 20%</p>
                </div>
                <Badge variant="destructive">Critical</Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg border-yellow-200 bg-yellow-50">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-yellow-800">Maintenance Due</p>
                  <p className="text-xs text-yellow-600">SteriBOT-003 scheduled maintenance</p>
                </div>
                <Badge variant="secondary">Warning</Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg border-blue-200 bg-blue-50">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-800">Task Completed</p>
                  <p className="text-xs text-blue-600">Operating Room 1 disinfection done</p>
                </div>
                <Badge variant="outline">Info</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>{t.dashboard.systemStatus}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">SteriBOT-001</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: "85%" }}></div>
              </div>
              <p className="text-xs text-muted-foreground">Battery: 85% | Location: Operating Room 1</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">SteriBOT-002</span>
                <Badge variant="secondary">Charging</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-yellow-600 h-2 rounded-full" style={{ width: "45%" }}></div>
              </div>
              <p className="text-xs text-muted-foreground">Battery: 45% | Location: Charging Station</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">SteriBOT-003</span>
                <Badge variant="outline">Offline</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-red-600 h-2 rounded-full" style={{ width: "0%" }}></div>
              </div>
              <p className="text-xs text-muted-foreground">Battery: 0% | Location: Maintenance Bay</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t.dashboard.performanceMetrics}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Disinfection Efficiency</span>
                <span className="text-sm font-medium">99.9%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Average Session Time</span>
                <span className="text-sm font-medium">42 min</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Uptime</span>
                <span className="text-sm font-medium">98.5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Areas Cleaned Today</span>
                <span className="text-sm font-medium">12</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t.dashboard.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              <Button variant="outline" className="justify-start">
                <Target className="w-4 h-4 mr-2" />
                Start New Session
              </Button>
              <Button variant="outline" className="justify-start">
                <Users className="w-4 h-4 mr-2" />
                View All Robots
              </Button>
              <Button variant="outline" className="justify-start">
                <Activity className="w-4 h-4 mr-2" />
                System Reports
              </Button>
              <Button variant="outline" className="justify-start">
                <Shield className="w-4 h-4 mr-2" />
                Security Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
