// lib/i18n/translations/ar.ts
import { Translations } from '../types';

export const ar: Translations = {
  login: {
    title: 'ستيريبوت',
    subtitle: 'جهاز التعقيم',
    welcome: 'مرحباً بعودتك! يرجى تسجيل الدخول إلى حسابك.',
    email: 'البريد الإلكتروني',
    emailPlaceholder: 'أدخل عنوان بريدك الإلكتروني',
    password: 'كلمة المرور',
    passwordPlaceholder: 'أدخل كلمة المرور',
    connect: 'اتصال',
    connecting: 'جاري الاتصال...',
    forgotPassword: 'نسيت كلمة المرور؟',
    emailRequired: 'البريد الإلكتروني مطلوب',
    emailInvalid: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
    passwordRequired: 'كلمة المرور مطلوبة',
    passwordMinLength: 'يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل',
    invalidCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    networkError: 'حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.',
    authenticationFailed: 'فشل في المصادقة. يرجى التحقق من بياناتك.'
  },
  
  network: {
    title: 'الاتصال عبر',
    subtitle: 'يرجى الاتصال بشبكة WiFi أو تفعيل بيانات الهاتف المحمول (4G/5G)',
    wifi: 'الاتصال عبر WiFi',
    wifiDescription: 'استخدام اتصال الشبكة اللاسلكية',
    mobile: 'الاتصال عبر بيانات الهاتف',
    mobileDescription: 'استخدام الشبكة الخلوية (4G/5G)',
    connect: 'اتصال',
    connecting: 'جاري الاتصال...'
  },
  
  connecting: {
    title: 'ستيريبوت',
    subtitle: 'ربط الروبوت',
    steps: {
      initializing: 'تهيئة الاتصال...',
      scanning: 'البحث عن الأجهزة...',
      establishing: 'إنشاء رابط آمن...',
      synchronizing: 'مزامنة البيانات...',
      established: 'تم إنشاء الاتصال!'
    },
    complete: '% مكتمل',
    lookingFor: 'البحث عن جهازك الذكي',
    ensureDevice: 'يرجى التأكد من تشغيل جهاز ستيريبوت وأنه في النطاق',
    wifi: 'واي فاي',
    mobileData: 'بيانات الهاتف'
  },
  
  robot: {
    title: 'ستيريبوت',
    subtitle: 'روبوت التطهير المستقل',
    connected: 'متصل',
    active: 'نشط',
    startScan: 'بدء المسح'
  },

  navigation: {
    home: 'الرئيسية',
    dashboard: 'لوحة التحكم',
    robotDetails: 'تفاصيل الروبوت',
    robotsList: 'قائمة الروبوتات',
    sterilizationHistory: 'تاريخ التعقيم',
    obstacleDetection: 'كشف العوائق',
    profile: 'الملف الشخصي',
    logout: 'تسجيل الخروج',
    authTest: 'اختبار المصادقة'
  },

  home: {
    title: 'مركز التحكم ستيريبوت',
    subtitle: 'نظام إدارة الروبوتات',
    configurationScanning: 'التكوين / مسح المساحة',
    robotStatus: 'حالة الروبوت',
    parameters: 'المعاملات',
    mapView: 'عرض الخريطة',
    cameraView: 'عرض الكاميرا',
    directionControls: 'عناصر التحكم في الاتجاه',
    reset: 'إعادة تعيين',
    startSterilization: 'بدء التعقيم',
    spaceConfiguration: 'تكوين المساحة',
    sterilizationConfig: 'تكوين التعقيم',
    position: 'الموضع',
    battery: 'البطارية',
    speed: 'السرعة',
    resolution: 'الدقة',
    scanRange: 'نطاق المسح',
    updateRate: 'معدل التحديث',
    stopScan: 'إيقاف المسح',
    saveMap: 'حفظ الخريطة',
    mapSaved: 'تم حفظ الخريطة!',
    resetMap: 'إعادة تعيين الخريطة'
  },

  dashboard: {
    title: 'لوحة التحكم',
    overview: 'نظرة عامة على النظام',
    statistics: 'الإحصائيات',
    activeConnections: 'الاتصالات النشطة',
    totalUsers: 'إجمالي المستخدمين',
    sterilizedAreas: 'المناطق المعقمة',
    efficiency: 'الكفاءة',
    bacteriaDetection: 'كشف البكتيريا',
    recentSessions: 'الجلسات الأخيرة',
    sessionHistory: 'تاريخ الجلسات',
    date: 'التاريخ',
    duration: 'المدة',
    area: 'المنطقة',
    status: 'الحالة',
    completed: 'مكتمل',
    inProgress: 'قيد التقدم',
    failed: 'فشل',
    todaysConnections: 'اتصالات اليوم',
    todaysUsers: 'مستخدمو اليوم',
    sterilizedZones: 'المناطق المعقمة',
    fromSterilizedZones: 'من 100 منطقة معقمة',
    operatingRoomA: 'غرفة العمليات أ',
    surgeryRoom: 'غرفة الجراحة',
    mainCorridor: 'الممر الرئيسي',
    operatingRoomB: 'غرفة العمليات ب',
    log: 'سجل',
    others: 'أخرى',
    bacteriaTypes: {
      eColi: 'الإشريكية القولونية',
      staphylococcus: 'المكورات العنقودية',
      pseudomonas: 'الزائفة',
      others: 'أخرى'
    },
    bacteriaDetected24h: 'البكتيريا المكتشفة (24 ساعة)',
    total: 'المجموع',
    zone: 'المنطقة',
    sterilizationLogChart: 'مخطط سجلات التعقيم',
    dateTime: 'التاريخ/الوقت',
    logStatus: 'حالة السجل',
    firstLog: 'السجل الأول',
    secondLog: 'السجل الثاني',
    thirdLog: 'السجل الثالث',
    fourthLog: 'السجل الرابع',
    fifthLog: 'السجل الخامس',
    sixthLog: 'السجل السادس',
    activeRobots: 'الروبوتات النشطة',
    totalRobots: 'إجمالي الروبوتات',
    criticalAlerts: 'التنبيهات الحرجة'
  },

  robotDetails: {
    title: 'تفاصيل الروبوت',
    information: 'معلومات الروبوت',
    model: 'الطراز',
    serialNumber: 'الرقم التسلسلي',
    status: 'الحالة',
    batteryLevel: 'مستوى البطارية',
    lastMaintenance: 'آخر صيانة',
    operatingHours: 'ساعات التشغيل',
    specifications: 'المواصفات',
    dimensions: 'الأبعاد',
    weight: 'الوزن',
    maxSpeed: 'السرعة القصوى',
    uvLamps: 'مصابيح الأشعة فوق البنفسجية',
    coverage: 'منطقة التغطية',
    maintenance: 'الصيانة',
    schedule: 'الجدولة',
    nextService: 'الخدمة التالية',
    serviceHistory: 'تاريخ الخدمة',
    weekDays: {
      mon: 'الإثنين',
      tue: 'الثلاثاء',
      wed: 'الأربعاء',
      thu: 'الخميس',
      fri: 'الجمعة',
      sat: 'السبت',
      sun: 'الأحد'
    },
    months: {
      january: 'يناير',
      february: 'فبراير',
      march: 'مارس',
      april: 'أبريل',
      may: 'مايو',
      june: 'يونيو',
      july: 'يوليو',
      august: 'أغسطس',
      september: 'سبتمبر',
      october: 'أكتوبر',
      november: 'نوفمبر',
      december: 'ديسمبر'
    },
    scheduledTasks: 'المهام المجدولة',
    operatingRoomSterilization: 'تعقيم غرفة العمليات أ',
    robotMaintenanceCheck: 'فحص صيانة الروبوت',
    preSurgeryProtocol: 'بروتوكول التعقيم قبل الجراحة',
    weeklyMaintenance: 'روتين الصيانة الأسبوعية',
    scheduled: 'مجدول',
    completed: 'مكتمل',
    inProgress: 'قيد التقدم',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    sterilization: 'التعقيم',
    maintenanceType: 'الصيانة',
    operatingRoomA: 'غرفة العمليات أ',
    robotStation: 'محطة الروبوت',
    drSmith: 'د. سميث',
    techTeam: 'الفريق التقني',
    mainCorridorCleaning: 'تنظيف الممر الرئيسي',
    emergencyRoomSterilization: 'تعقيم غرفة الطوارئ',
    dailyCorridorSterilization: 'تعقيم الممر اليومي',
    postIncidentSterilization: 'تعقيم ما بعد الحادث',
    mainCorridor: 'الممر الرئيسي',
    emergencyRoom: 'غرفة الطوارئ',
    nightShift: 'الوردية الليلية',
    drJohnson: 'د. جونسون',
    showCalendar: 'إظهار التقويم',
    hideCalendar: 'إخفاء التقويم',
    edit: 'تحرير',
    day: 'يوم',
    week: 'أسبوع',
    month: 'شهر',
    year: 'سنة',
    addTask: 'إضافة مهمة',
    taskTitle: 'عنوان المهمة',
    date: 'التاريخ',
    time: 'الوقت',
    duration: 'المدة (بالدقائق)',
    priority: 'الأولوية',
    roomLocation: 'الغرفة/الموقع',
    assignedTo: 'مُسند إلى',
    notes: 'ملاحظات',
    create: 'إنشاء',
    cancel: 'إلغاء',
    editTask: 'تحرير المهمة',
    deleteTask: 'حذف المهمة',
    save: 'حفظ',
    minutes: 'دقائق'
  },

  robotsList: {
    title: 'قائمة الروبوتات',
    addRobot: 'إضافة روبوت',
    addSpace: 'إضافة مساحة',
    robotDescription: 'روبوت التطهير المستقل',
    startSterilization: 'بدء التعقيم',
    name: 'الاسم',
    location: 'الموقع',
    description: 'الوصف',
    roomType: 'نوع الغرفة',
    area: 'المنطقة',
    addRobotTitle: 'إضافة روبوت جديد',
    addSpaceTitle: 'إضافة مساحة جديدة',
    cancel: 'إلغاء',
    add: 'إضافة',
    addNewRobot: 'إضافة روبوت جديد',
    robotName: 'اسم الروبوت',
    enterRobotName: 'أدخل اسم الروبوت',
    selectModel: 'اختر الطراز',
    serialNumber: 'الرقم التسلسلي',
    enterSerialNumber: 'أدخل الرقم التسلسلي',
    enterLocation: 'أدخل الموقع',
    enterDescription: 'أدخل الوصف',
    addNewSpace: 'إضافة مساحة جديدة',
    spaceName: 'اسم المساحة',
    enterSpaceName: 'أدخل اسم المساحة',
    selectRoomType: 'اختر نوع الغرفة',
    enterArea: 'أدخل المنطقة',
    operatingRoom: 'غرفة العمليات',
    surgeryRoom: 'غرفة الجراحة',
    corridor: 'الممر',
    laboratory: 'المختبر'
  },

  profile: {
    title: 'الملف الشخصي',
    personalInfo: 'المعلومات الشخصية',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    role: 'الدور',
    department: 'القسم',
    phone: 'الهاتف',
    settings: 'الإعدادات',
    notifications: 'الإشعارات',
    language: 'اللغة',
    timezone: 'المنطقة الزمنية',
    theme: 'المظهر',
    security: 'الأمان',
    changePassword: 'تغيير كلمة المرور',
    twoFactor: 'المصادقة الثنائية',
    loginHistory: 'تاريخ تسجيل الدخول',
    editProfile: 'تعديل الملف الشخصي',
    accountStatus: 'حالة الحساب',
    memberSince: 'عضو منذ',
    lastLogin: 'آخر تسجيل دخول',
    accessLevel: 'مستوى الوصول',
    administrator: 'مدير',
    details: 'التفاصيل',
    lastName: 'اسم العائلة',
    jobTitle: 'المسمى الوظيفي',
    doctor: 'طبيب',
    cardiology: 'أمراض القلب',
    securitySettings: 'إجراءات سريعة',
    downloadData: 'تحميل تقرير النشاط',
    notifications2FA: 'إعدادات الإشعارات',
    logoutAllDevices: 'تسجيل الخروج',
    today: 'اليوم',
    january2024: 'يناير 2024'
  },

  sterilizationHistory: {
    title: 'تاريخ التعقيم',
    history: 'التاريخ',
    filter: 'تصفية',
    dateRange: 'نطاق التاريخ',
    robotFilter: 'مرشح الروبوت',
    statusFilter: 'مرشح الحالة',
    export: 'تصدير',
    details: 'التفاصيل',
    startTime: 'وقت البداية',
    endTime: 'وقت النهاية',
    coverage: 'التغطية',
    effectiveness: 'الفعالية',
    robotDescription: 'روبوت التطهير المستقل',
    activityHistory: 'تاريخ النشاط',
    bacteria: 'البكتيريا',
    staphAureus: 'المكورات العنقودية الذهبية',
    log: 'سجل',
    log4: 'سجل 4',
    operationsHistory: 'تاريخ العمليات',
    id: 'المعرف',
    zone: 'المنطقة',
    duration: 'المدة',
    status: 'الحالة',
    efficiency: 'الكفاءة',
    operationDetails: 'تفاصيل العملية',
    operationId: 'معرف العملية',
    resolve: 'حل',
    close: 'إغلاق',
    all: 'الكل',
    completed: 'مكتمل',
    inProgress: 'قيد التقدم',
    failed: 'حادث',
    exportData: 'تصدير البيانات',
    refresh: 'تحديث'
  },

  obstacleDetection: {
    title: 'كشف العوائق',
    detection: 'نظام الكشف',
    sensors: 'أجهزة الاستشعار',
    alerts: 'التنبيهات',
    sensitivity: 'الحساسية',
    calibration: 'المعايرة',
    testMode: 'وضع الاختبار',
    sensorStatus: 'حالة المستشعر',
    frontSensor: 'المستشعر الأمامي',
    rearSensor: 'المستشعر الخلفي',
    sideSensors: 'المستشعرات الجانبية',
    operational: 'تشغيلي',
    warning: 'تحذير',
    error: 'خطأ',
    allTypes: 'جميع الأنواع',
    connection: 'الاتصال',
    battery: 'البطارية',
    human: 'إنسان',
    allSeverities: 'جميع مستويات الخطورة',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    severity: 'الخطورة',
    type: 'النوع',
    time: 'الوقت',
    location: 'الموقع',
    action: 'الإجراء',
    resolve: 'حل',
    alertDetails: 'تفاصيل التنبيه',
    alertId: 'معرف التنبيه',
    description: 'الوصف',
    resolveAlert: 'حل التنبيه',
    resolutionNote: 'ملاحظة الحل',
    enterNote: 'أدخل ملاحظة الحل',
    markResolved: 'وضع علامة كمحلول',
    cancel: 'إلغاء',
    close: 'إغلاق',
    robotStopped: 'توقف الروبوت بسبب عائق',
    lowBattery: 'تحذير انخفاض البطارية',
    humanDetected: 'تم اكتشاف إنسان في منطقة التعقيم',
    connectionLost: 'فقدان الاتصال مع الروبوت',
    resolved: 'محلول',
    pending: 'معلق',
    critical: 'حرج',
    info: 'معلومات'
  },

  modals: {
    spaceConfig: {
      title: 'تكوين المساحة',
      roomType: 'نوع الغرفة',
      dimensions: 'الأبعاد',
      obstacles: 'العوائق',
      specialRequirements: 'المتطلبات الخاصة',
      save: 'حفظ التكوين',
      cancel: 'إلغاء'
    },
    sterilizationConfig: {
      title: 'تكوين التعقيم',
      mode: 'وضع التعقيم',
      intensity: 'شدة الأشعة فوق البنفسجية',
      duration: 'المدة',
      schedule: 'الجدولة',
      start: 'بدء التعقيم',
      cancel: 'إلغاء'
    }
  },

  status: {
    online: 'متصل',
    offline: 'غير متصل',
    charging: 'يشحن',
    working: 'يعمل',
    idle: 'خامل',
    maintenance: 'صيانة',
    error: 'خطأ',
    connected: 'متصل',
    disconnected: 'منقطع',
    active: 'نشط',
    inactive: 'غير نشط',
    resolved: 'محلول'
  },

  common: {
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    close: 'إغلاق',
    save: 'حفظ',
    edit: 'تحرير',
    delete: 'حذف',
    add: 'إضافة',
    remove: 'إزالة',
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    warning: 'تحذير',
    info: 'معلومات',
    yes: 'نعم',
    no: 'لا',
    ok: 'موافق',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    finish: 'إنهاء',
    retry: 'إعادة المحاولة',
    refresh: 'تحديث'
  },

  errors: {
    networkError: 'خطأ في الشبكة - يرجى التحقق من اتصالك',
    timeout: 'انتهت مهلة الطلب - يرجى المحاولة مرة أخرى',
    authRequired: 'مطلوب مصادقة - يرجى تسجيل الدخول',
    accessDenied: 'تم رفض الوصول - صلاحيات غير كافية',
    notFound: 'المورد غير موجود',
    serverError: 'خطأ في الخادم - يرجى المحاولة لاحقاً',
    invalidData: 'بيانات غير صالحة مقدمة',
    connectionFailed: 'فشل الاتصال',
    loadingFailed: 'فشل في تحميل البيانات',
    saveFailed: 'فشل في حفظ التغييرات',
    deleteFailed: 'فشل في حذف العنصر',
    updateFailed: 'فشل في تحديث العنصر',
    exportFailed: 'فشل في تصدير البيانات',
    importFailed: 'فشل في استيراد البيانات',
    validationError: 'خطأ في التحقق',
    unknownError: 'حدث خطأ غير معروف'
  },

  loading: {
    general: 'جاري التحميل...',
    robots: 'جاري تحميل الروبوتات...',
    sessions: 'جاري تحميل الجلسات...',
    alerts: 'جاري تحميل التنبيهات...',
    dashboard: 'جاري تحميل لوحة التحكم...',
    saving: 'جاري الحفظ...',
    deleting: 'جاري الحذف...',
    updating: 'جاري التحديث...',
    exporting: 'جاري التصدير...',
    importing: 'جاري الاستيراد...',
    connecting: 'جاري الاتصال...'
  },

  auth: {
    testInterface: 'واجهة اختبار المصادقة',
    testMode: 'وضع الاختبار',
    loginForm: 'نموذج تسجيل الدخول',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    enterEmail: 'أدخل عنوان البريد الإلكتروني',
    enterPassword: 'أدخل كلمة المرور',
    testLogin: 'اختبار تسجيل الدخول',
    testing: 'جاري الاختبار...',
    testAccounts: 'حسابات الاختبار',
    testResults: 'نتائج الاختبار',
    loginSuccess: 'تم تسجيل الدخول بنجاح!',
    invalidCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    tokenGenerated: 'الرمز المميز المُولد',
    userData: 'بيانات المستخدم',
    copyToken: 'نسخ الرمز المميز',
    tokenCopied: 'تم نسخ الرمز المميز!'
  }
};
