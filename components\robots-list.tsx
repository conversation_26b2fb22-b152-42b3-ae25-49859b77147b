"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTranslation } from "@/lib/i18n/context"

export function RobotsList() {
  const t = useTranslation()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Robots Management</h1>
        <p className="text-muted-foreground">Static view - no API calls</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>SteriBOT Fleet</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">SteriBOT-001</h3>
                <p className="text-sm text-muted-foreground">Operating Room 1</p>
              </div>
              <Badge variant="default">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">SteriBOT-002</h3>
                <p className="text-sm text-muted-foreground">Charging Station</p>
              </div>
              <Badge variant="secondary">Charging</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">SteriBOT-003</h3>
                <p className="text-sm text-muted-foreground">Maintenance Bay</p>
              </div>
              <Badge variant="destructive">Offline</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
