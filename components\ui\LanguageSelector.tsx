// components/ui/LanguageSelector.tsx
"use client"

import { useState, useRef, useEffect } from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { useLanguage } from '@/lib/i18n/context';
import { LANGUAGES, Language } from '@/lib/i18n/types';

export function LanguageSelector() {
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentLanguage = LANGUAGES.find(lang => lang.code === language) || LANGUAGES[0];

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageSelect = (langCode: Language) => {
    setLanguage(langCode);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="language-selector-button w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border-2 border-teal-300/30 flex items-center justify-center hover:bg-white/20 transition-all duration-300 shadow-lg group"
        aria-label="Select Language"
      >
        <div className="relative flex items-center justify-center">
          <span className="text-2xl">{currentLanguage.flag}</span>
          <ChevronDown 
            className={`absolute -bottom-1 -right-1 w-3 h-3 text-teal-600 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="language-dropdown absolute top-14 right-0 w-64 bg-white/95 backdrop-blur-md rounded-xl border border-teal-200/30 shadow-3d overflow-hidden z-50">
          {/* 3D Depth Layers */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
          <div className="absolute inset-1 rounded-xl bg-gradient-to-tl from-teal-100/30 to-transparent pointer-events-none"></div>
          
          <div className="relative z-10 py-2">
            {LANGUAGES.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageSelect(lang.code)}
                className={`w-full px-4 py-3 flex items-center gap-3 hover:bg-teal-50/80 transition-all duration-200 ${
                  language === lang.code ? 'bg-teal-100/50' : ''
                }`}
              >
                {/* Flag */}
                <span className="text-xl">{lang.flag}</span>
                
                {/* Language Names */}
                <div className="flex-1 text-left">
                  <div className="text-teal-800 font-medium">{lang.name}</div>
                  <div className="text-teal-600 text-sm">{lang.nativeName}</div>
                </div>
                
                {/* Check Mark */}
                {language === lang.code && (
                  <Check className="w-5 h-5 text-teal-600" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .language-selector-button {
          transform: perspective(1000px) rotateX(2deg) rotateY(-2deg);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .language-selector-button:hover {
          transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.05);
          box-shadow: 0 10px 25px rgba(20, 184, 166, 0.2);
        }

        .language-dropdown {
          transform: perspective(1000px) rotateX(-5deg);
          animation: dropdownSlide 0.3s ease-out forwards;
        }

        @keyframes dropdownSlide {
          from {
            opacity: 0;
            transform: perspective(1000px) rotateX(-15deg) translateY(-10px);
          }
          to {
            opacity: 1;
            transform: perspective(1000px) rotateX(0deg) translateY(0px);
          }
        }
      `}</style>
    </div>
  );
}
