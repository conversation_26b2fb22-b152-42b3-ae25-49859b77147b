// components/Login.tsx
"use client"

import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import { NetworkConfigModal } from "@/components/ui/NetworkConfigModal";
import { ConnectingInterface } from "@/components/ConnectingInterface";
import { RobotInterface } from "@/components/RobotInterface";
import { LanguageSelector } from "@/components/ui/LanguageSelector";
import { useTranslation } from "@/lib/i18n/context";
import { authService } from "@/lib/api/services/auth";

interface LoginProps {
  onLogin: (email: string, password: string) => void;
}

export function Login({ onLogin }: LoginProps) {
  const t = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({ email: '', password: '' });
  const [showNetworkModal, setShowNetworkModal] = useState(false);
  const [showConnectingInterface, setShowConnectingInterface] = useState(false);
  const [showRobotInterface, setShowRobotInterface] = useState(false);
  const [selectedConnectionType, setSelectedConnectionType] = useState<'wifi' | 'mobile'>('wifi');

  const validateForm = () => {
    const newErrors = { email: '', password: '' };
    let isValid = true;

    if (!email.trim()) {
      newErrors.email = t.login.emailRequired;
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t.login.emailInvalid;
      isValid = false;
    }

    if (!password.trim()) {
      newErrors.password = t.login.passwordRequired;
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = t.login.passwordMinLength;
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    // Clear any previous password errors
    setErrors(prev => ({ ...prev, password: '' }));

    try {
      // Call real authentication service - ONLY API CALL
      await authService.login({ email, password });

      // Authentication successful - token is already stored in localStorage
      setIsLoading(false);
      setShowNetworkModal(true);
    } catch (error: any) {
      // Network or other error
      setIsLoading(false);
      setErrors(prev => ({
        ...prev,
        password: error.message || t.login.networkError || 'Network error occurred'
      }));
    }
  };

  const handleForgotPassword = () => {
    // Handle forgot password logic
    console.log('Forgot password clicked');
  };

  const handleNetworkConnect = (connectionType: 'wifi' | 'mobile') => {
    console.log('Connecting with:', connectionType);
    setSelectedConnectionType(connectionType);
    setShowNetworkModal(false);
    setShowConnectingInterface(true);
  };

  const handleNetworkModalClose = () => {
    setShowNetworkModal(false);
  };

  const handleConnectionComplete = () => {
    setShowConnectingInterface(false);
    setShowRobotInterface(true);
  };

  const handleStartScan = () => {
    setShowRobotInterface(false);
    onLogin(email, password);
  };

  // Show robot interface after connection is complete
  if (showRobotInterface) {
    return (
      <RobotInterface
        onStartScan={handleStartScan}
      />
    );
  }

  // Show connecting interface if in connection process
  if (showConnectingInterface) {
    return (
      <ConnectingInterface
        connectionType={selectedConnectionType}
        onConnectionComplete={handleConnectionComplete}
      />
    );
  }

  return (
    <div className="min-h-screen login-container flex items-center justify-center p-4">
      {/* Blurred Background */}
      <div className="absolute inset-0 login-background"></div>

      {/* Language Selector */}
      <div className="absolute top-6 right-6 z-20">
        <LanguageSelector />
      </div>

      {/* Login Modal with 3D Effects */}
      <div className="relative z-10 w-full max-w-md">
        <div className="login-modal-3d rounded-xl p-8 border border-teal-200/30 bg-teal-50/95 backdrop-blur-md shadow-3d">
          {/* 3D Depth Layer */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent pointer-events-none"></div>
          <div className="relative z-10">
            {/* Logo/Title Section */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-teal-600 to-teal-800 flex items-center justify-center shadow-lg">
                  <img
                    src="/images/image.png"
                    alt="SteriBOT Logo"
                    className="w-10 h-10 object-cover rounded-full"
                    onError={(e) => {
                      // Fallback if image doesn't exist
                      e.currentTarget.style.display = 'none';
                      (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'flex';
                    }}
                  />
                  <div
                    className="w-10 h-10 bg-gradient-to-br from-teal-600 to-teal-800 rounded-full items-center justify-center text-white font-bold text-lg hidden"
                  >
                    S
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-teal-800">{t.login.title}</h1>
                  <p className="text-teal-600 text-sm">{t.login.subtitle}</p>
                </div>
              </div>
              <p className="text-teal-700 text-sm">
                {t.login.welcome}
              </p>
            </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-teal-800">
                {t.login.email}
              </Label>
              <Input
                id="email"
                type="email"
                placeholder={t.login.emailPlaceholder}
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (errors.email) setErrors(prev => ({ ...prev, email: '' }));
                }}
                className={`bg-white/80 border-teal-300 text-teal-800 placeholder-teal-500 focus:border-teal-600 focus:ring-teal-200 shadow-inner ${
                  errors.email ? 'border-red-400 focus:border-red-400' : ''
                }`}
                disabled={isLoading}
              />
              {errors.email && (
                <p className="text-red-600 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-teal-800">
                {t.login.password}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder={t.login.passwordPlaceholder}
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    if (errors.password) setErrors(prev => ({ ...prev, password: '' }));
                  }}
                  className={`bg-white/80 border-teal-300 text-teal-800 placeholder-teal-500 focus:border-teal-600 focus:ring-teal-200 pr-10 shadow-inner ${
                    errors.password ? 'border-red-400 focus:border-red-400' : ''
                  }`}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-600 hover:text-teal-800 transition-colors"
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-600 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            {/* Connect Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full text-white py-3 px-4 rounded-lg font-medium transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 connect-button shadow-lg"
              style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  {t.login.connecting}
                </div>
              ) : (
                t.login.connect
              )}
            </Button>

            {/* Forgot Password Link */}
            <div className="text-center">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="text-teal-600 hover:text-teal-800 text-sm transition-colors underline hover:no-underline"
                disabled={isLoading}
              >
{t.login.forgotPassword}
              </button>
            </div>
            </form>
          </div>
        </div>
      </div>

      {/* Network Configuration Modal */}
      <NetworkConfigModal
        isOpen={showNetworkModal}
        onClose={handleNetworkModalClose}
        onConnect={handleNetworkConnect}
      />
    </div>
  );
}
