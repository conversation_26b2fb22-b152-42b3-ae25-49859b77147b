"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTranslation } from "@/lib/i18n/context"

export function RobotDetails() {
  const t = useTranslation()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Robot Details</h1>
        <p className="text-muted-foreground">Static view - no API calls</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>SteriBOT-001 Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h3 className="font-medium mb-2">Status</h3>
              <Badge variant="default">Active</Badge>
            </div>
            <div>
              <h3 className="font-medium mb-2">Location</h3>
              <p className="text-sm text-muted-foreground">Operating Room 1</p>
            </div>
            <div>
              <h3 className="font-medium mb-2">Battery Level</h3>
              <p className="text-sm text-muted-foreground">85%</p>
            </div>
            <div>
              <h3 className="font-medium mb-2">Last Update</h3>
              <p className="text-sm text-muted-foreground">2 minutes ago</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
