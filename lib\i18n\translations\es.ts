// lib/i18n/translations/es.ts
import { Translations } from '../types';

export const es: Translations = {
  login: {
    title: 'SteriBOT',
    subtitle: 'ESTERILIZADOR',
    welcome: '¡Bienvenido de nuevo! Por favor, inicia sesión en tu cuenta.',
    email: 'Correo electrónico',
    emailPlaceholder: 'Ingresa tu dirección de correo electrónico',
    password: 'Contraseña',
    passwordPlaceholder: 'Ingresa tu contraseña',
    connect: 'Conectar',
    connecting: 'Conectando...',
    forgotPassword: '¿Olvidaste tu contraseña?',
    emailRequired: 'El correo electrónico es requerido',
    emailInvalid: 'Por favor ingresa una dirección de correo electrónico válida',
    passwordRequired: 'La contraseña es requerida',
    passwordMinLength: 'La contraseña debe tener al menos 6 caracteres',
    invalidCredentials: 'Correo electrónico o contraseña inválidos',
    networkError: 'Error de red. Por favor intenta de nuevo.',
    authenticationFailed: 'Falló la autenticación. Verifica tus credenciales.'
  },
  
  network: {
    title: 'Conectar con',
    subtitle: 'Por favor conéctate a una red WiFi o habilita tus datos móviles (4G/5G)',
    wifi: 'Conectar con WiFi',
    wifiDescription: 'Usar conexión de red inalámbrica',
    mobile: 'Conectar con datos móviles',
    mobileDescription: 'Usar red celular (4G/5G)',
    connect: 'Conectar',
    connecting: 'Conectando...'
  },
  
  connecting: {
    title: 'SteriBOT',
    subtitle: 'Conectando robot',
    steps: {
      initializing: 'Inicializando conexión...',
      scanning: 'Escaneando dispositivos...',
      establishing: 'Estableciendo enlace seguro...',
      synchronizing: 'Sincronizando datos...',
      established: '¡Conexión establecida!'
    },
    complete: '% Completado',
    lookingFor: 'Buscando tu dispositivo inteligente',
    ensureDevice: 'Por favor asegúrate de que tu dispositivo SteriBOT esté encendido y dentro del alcance',
    wifi: 'WiFi',
    mobileData: 'Datos móviles'
  },
  
  robot: {
    title: 'SteriBOT',
    subtitle: 'Robot de desinfección autónomo',
    connected: 'Conectado',
    active: 'Activo',
    startScan: 'Iniciar escaneo'
  },

  navigation: {
    home: 'Inicio',
    dashboard: 'Panel de control',
    robotDetails: 'Detalles del robot',
    robotsList: 'Lista de robots',
    sterilizationHistory: 'Historial de esterilización',
    obstacleDetection: 'Detección de obstáculos',
    profile: 'Perfil',
    logout: 'Cerrar sesión',
    authTest: 'Prueba Auth'
  },

  home: {
    title: 'Centro de control SteriBOT',
    subtitle: 'Sistema de gestión de robots',
    configurationScanning: 'Configuración / Escaneo del espacio',
    robotStatus: 'Estado del robot',
    parameters: 'Parámetros',
    mapView: 'Vista del mapa',
    cameraView: 'Vista de cámara',
    directionControls: 'Controles de dirección',
    reset: 'Reiniciar',
    startSterilization: 'Iniciar esterilización',
    spaceConfiguration: 'Configuración del espacio',
    sterilizationConfig: 'Configuración de esterilización',
    position: 'Posición',
    battery: 'Batería',
    speed: 'Velocidad',
    resolution: 'Resolución',
    scanRange: 'Rango de escaneo',
    updateRate: 'Tasa de actualización',
    stopScan: 'Detener escaneo',
    saveMap: 'Guardar mapa',
    mapSaved: '¡Mapa guardado!',
    resetMap: 'Reiniciar mapa'
  },

  dashboard: {
    title: 'Panel de control',
    overview: 'Resumen del sistema',
    statistics: 'Estadísticas',
    activeConnections: 'Conexiones activas',
    totalUsers: 'Usuarios totales',
    sterilizedAreas: 'Áreas esterilizadas',
    efficiency: 'Eficiencia',
    bacteriaDetection: 'Detección de bacterias',
    recentSessions: 'Sesiones recientes',
    sessionHistory: 'Historial de sesiones',
    date: 'Fecha',
    duration: 'Duración',
    area: 'Área',
    status: 'Estado',
    completed: 'Completado',
    inProgress: 'En progreso',
    failed: 'Fallido',
    todaysConnections: 'Conexiones de hoy',
    todaysUsers: 'Usuarios de hoy',
    sterilizedZones: 'Zonas esterilizadas',
    fromSterilizedZones: 'de 100 zonas esterilizadas',
    operatingRoomA: 'Quirófano A',
    surgeryRoom: 'Sala de cirugía',
    mainCorridor: 'Pasillo principal',
    operatingRoomB: 'Quirófano B',
    log: 'Registro',
    others: 'Otros',
    bacteriaTypes: {
      eColi: 'E. coli',
      staphylococcus: 'Estafilococo',
      pseudomonas: 'Pseudomonas',
      others: 'Otros'
    },
    bacteriaDetected24h: 'Bacterias detectadas (24h)',
    total: 'Total',
    zone: 'Zona',
    sterilizationLogChart: 'Gráfico de registros de esterilización',
    dateTime: 'Fecha/Hora',
    logStatus: 'Estado del registro',
    firstLog: '1er Registro',
    secondLog: '2do Registro',
    thirdLog: '3er Registro',
    fourthLog: '4to Registro',
    fifthLog: '5to Registro',
    sixthLog: '6to Registro',
    activeRobots: 'Robots Activos',
    totalRobots: 'Total Robots',
    criticalAlerts: 'Alertas Críticas'
  },

  robotDetails: {
    title: 'Detalles del robot',
    information: 'Información del robot',
    model: 'Modelo',
    serialNumber: 'Número de serie',
    status: 'Estado',
    batteryLevel: 'Nivel de batería',
    lastMaintenance: 'Último mantenimiento',
    operatingHours: 'Horas de operación',
    specifications: 'Especificaciones',
    dimensions: 'Dimensiones',
    weight: 'Peso',
    maxSpeed: 'Velocidad máxima',
    uvLamps: 'Lámparas UV',
    coverage: 'Área de cobertura',
    maintenance: 'Mantenimiento',
    schedule: 'Programación',
    nextService: 'Próximo servicio',
    serviceHistory: 'Historial de servicio',
    weekDays: {
      mon: 'Lun',
      tue: 'Mar',
      wed: 'Mié',
      thu: 'Jue',
      fri: 'Vie',
      sat: 'Sáb',
      sun: 'Dom'
    },
    months: {
      january: 'Enero',
      february: 'Febrero',
      march: 'Marzo',
      april: 'Abril',
      may: 'Mayo',
      june: 'Junio',
      july: 'Julio',
      august: 'Agosto',
      september: 'Septiembre',
      october: 'Octubre',
      november: 'Noviembre',
      december: 'Diciembre'
    },
    scheduledTasks: 'Tareas programadas',
    operatingRoomSterilization: 'Esterilización quirófano A',
    robotMaintenanceCheck: 'Verificación mantenimiento robot',
    preSurgeryProtocol: 'Protocolo de esterilización pre-cirugía',
    weeklyMaintenance: 'Rutina de mantenimiento semanal',
    scheduled: 'Programado',
    completed: 'Completado',
    inProgress: 'En progreso',
    high: 'Alto',
    medium: 'Medio',
    low: 'Bajo',
    sterilization: 'Esterilización',
    maintenanceType: 'Mantenimiento',
    operatingRoomA: 'Quirófano A',
    robotStation: 'Estación del robot',
    drSmith: 'Dr. Smith',
    techTeam: 'Equipo técnico',
    mainCorridorCleaning: 'Limpieza pasillo principal',
    emergencyRoomSterilization: 'Esterilización sala de emergencias',
    dailyCorridorSterilization: 'Esterilización diaria del pasillo',
    postIncidentSterilization: 'Esterilización post-incidente',
    mainCorridor: 'Pasillo principal',
    emergencyRoom: 'Sala de emergencias',
    nightShift: 'Turno nocturno',
    drJohnson: 'Dr. Johnson',
    showCalendar: 'Mostrar calendario',
    hideCalendar: 'Ocultar calendario',
    edit: 'Editar',
    day: 'Día',
    week: 'Semana',
    month: 'Mes',
    year: 'Año',
    addTask: 'Agregar tarea',
    taskTitle: 'Título de la tarea',
    date: 'Fecha',
    time: 'Hora',
    duration: 'Duración (minutos)',
    priority: 'Prioridad',
    roomLocation: 'Sala/Ubicación',
    assignedTo: 'Asignado a',
    notes: 'Notas',
    create: 'Crear',
    cancel: 'Cancelar',
    editTask: 'Editar tarea',
    deleteTask: 'Eliminar tarea',
    save: 'Guardar',
    minutes: 'minutos'
  },

  robotsList: {
    title: 'Lista de robots',
    addRobot: 'Agregar robot',
    addSpace: 'Agregar espacio',
    robotDescription: 'Robot de desinfección autónomo',
    startSterilization: 'Iniciar esterilización',
    name: 'Nombre',
    location: 'Ubicación',
    description: 'Descripción',
    roomType: 'Tipo de habitación',
    area: 'Área',
    addRobotTitle: 'Agregar nuevo robot',
    addSpaceTitle: 'Agregar nuevo espacio',
    cancel: 'Cancelar',
    add: 'Agregar',
    addNewRobot: 'Agregar nuevo robot',
    robotName: 'Nombre del robot',
    enterRobotName: 'Ingrese el nombre del robot',
    selectModel: 'Seleccionar modelo',
    serialNumber: 'Número de serie',
    enterSerialNumber: 'Ingrese el número de serie',
    enterLocation: 'Ingrese la ubicación',
    enterDescription: 'Ingrese la descripción',
    addNewSpace: 'Agregar nuevo espacio',
    spaceName: 'Nombre del espacio',
    enterSpaceName: 'Ingrese el nombre del espacio',
    selectRoomType: 'Seleccionar tipo de habitación',
    enterArea: 'Ingrese el área',
    operatingRoom: 'Quirófano',
    surgeryRoom: 'Sala de cirugía',
    corridor: 'Pasillo',
    laboratory: 'Laboratorio'
  },

  profile: {
    title: 'Perfil',
    personalInfo: 'Información personal',
    name: 'Nombre',
    email: 'Correo electrónico',
    role: 'Rol',
    department: 'Departamento',
    phone: 'Teléfono',
    settings: 'Configuraciones',
    notifications: 'Notificaciones',
    language: 'Idioma',
    timezone: 'Zona horaria',
    theme: 'Tema',
    security: 'Seguridad',
    changePassword: 'Cambiar contraseña',
    twoFactor: 'Autenticación de dos factores',
    loginHistory: 'Historial de inicio de sesión',
    editProfile: 'Editar perfil',
    accountStatus: 'Estado de la cuenta',
    memberSince: 'Miembro desde',
    lastLogin: 'Último inicio de sesión',
    accessLevel: 'Nivel de acceso',
    administrator: 'Administrador',
    details: 'Detalles',
    lastName: 'Apellido',
    jobTitle: 'Título del trabajo',
    doctor: 'Doctor',
    cardiology: 'Cardiología',
    securitySettings: 'Acciones rápidas',
    downloadData: 'Descargar informe de actividad',
    notifications2FA: 'Configuración de notificaciones',
    logoutAllDevices: 'Cerrar sesión',
    today: 'Hoy',
    january2024: 'Enero 2024'
  },

  sterilizationHistory: {
    title: 'Historial de esterilización',
    history: 'Historial',
    filter: 'Filtrar',
    dateRange: 'Rango de fechas',
    robotFilter: 'Filtro de robot',
    statusFilter: 'Filtro de estado',
    export: 'Exportar',
    details: 'Detalles',
    startTime: 'Hora de inicio',
    endTime: 'Hora de finalización',
    coverage: 'Cobertura',
    effectiveness: 'Efectividad',
    robotDescription: 'Robot de desinfección autónomo',
    activityHistory: 'Historial de actividad',
    bacteria: 'Bacteria',
    staphAureus: 'Estafilococo aureus',
    log: 'Registro',
    log4: 'Registro 4',
    operationsHistory: 'Historial de operaciones',
    id: 'ID',
    zone: 'Zona',
    duration: 'Duración',
    status: 'Estado',
    efficiency: 'Eficiencia',
    operationDetails: 'Detalles de la operación',
    operationId: 'ID de operación',
    resolve: 'Resolver',
    close: 'Cerrar',
    all: 'Todos',
    completed: 'Completado',
    inProgress: 'En progreso',
    failed: 'Incidente',
    exportData: 'Exportar datos',
    refresh: 'Actualizar'
  },

  obstacleDetection: {
    title: 'Detección de obstáculos',
    detection: 'Sistema de detección',
    sensors: 'Sensores',
    alerts: 'Alertas',
    sensitivity: 'Sensibilidad',
    calibration: 'Calibración',
    testMode: 'Modo de prueba',
    sensorStatus: 'Estado del sensor',
    frontSensor: 'Sensor frontal',
    rearSensor: 'Sensor trasero',
    sideSensors: 'Sensores laterales',
    operational: 'Operacional',
    warning: 'Advertencia',
    error: 'Error',
    allTypes: 'Todos los tipos',
    connection: 'Conexión',
    battery: 'Batería',
    human: 'Humano',
    allSeverities: 'Todas las severidades',
    high: 'Alto',
    medium: 'Medio',
    low: 'Bajo',
    severity: 'Severidad',
    type: 'Tipo',
    time: 'Hora',
    location: 'Ubicación',
    action: 'Acción',
    resolve: 'Resolver',
    alertDetails: 'Detalles de la alerta',
    alertId: 'ID de alerta',
    description: 'Descripción',
    resolveAlert: 'Resolver alerta',
    resolutionNote: 'Nota de resolución',
    enterNote: 'Ingrese nota de resolución',
    markResolved: 'Marcar como resuelto',
    cancel: 'Cancelar',
    close: 'Cerrar',
    robotStopped: 'Robot detenido debido a obstáculo',
    lowBattery: 'Advertencia de batería baja',
    humanDetected: 'Humano detectado en zona de esterilización',
    connectionLost: 'Conexión perdida con el robot',
    resolved: 'Resuelto',
    pending: 'Pendiente',
    critical: 'Crítico',
    info: 'Información'
  },

  modals: {
    spaceConfig: {
      title: 'Configuración del espacio',
      roomType: 'Tipo de habitación',
      dimensions: 'Dimensiones',
      obstacles: 'Obstáculos',
      specialRequirements: 'Requisitos especiales',
      save: 'Guardar configuración',
      cancel: 'Cancelar'
    },
    sterilizationConfig: {
      title: 'Configuración de esterilización',
      mode: 'Modo de esterilización',
      intensity: 'Intensidad UV',
      duration: 'Duración',
      schedule: 'Programación',
      start: 'Iniciar esterilización',
      cancel: 'Cancelar'
    }
  },

  status: {
    online: 'En línea',
    offline: 'Fuera de línea',
    charging: 'Cargando',
    working: 'Trabajando',
    idle: 'Inactivo',
    maintenance: 'Mantenimiento',
    error: 'Error',
    connected: 'Conectado',
    disconnected: 'Desconectado',
    active: 'Activo',
    inactive: 'Inactivo',
    resolved: 'Resuelto'
  },

  common: {
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    close: 'Cerrar',
    save: 'Guardar',
    edit: 'Editar',
    delete: 'Eliminar',
    add: 'Agregar',
    remove: 'Quitar',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    warning: 'Advertencia',
    info: 'Información',
    yes: 'Sí',
    no: 'No',
    ok: 'OK',
    back: 'Atrás',
    next: 'Siguiente',
    previous: 'Anterior',
    finish: 'Finalizar',
    retry: 'Reintentar',
    refresh: 'Actualizar'
  },

  errors: {
    networkError: 'Error de red - por favor verifica tu conexión',
    timeout: 'Tiempo de espera agotado - por favor intenta de nuevo',
    authRequired: 'Autenticación requerida - por favor inicia sesión',
    accessDenied: 'Acceso denegado - permisos insuficientes',
    notFound: 'Recurso no encontrado',
    serverError: 'Error del servidor - por favor intenta más tarde',
    invalidData: 'Datos inválidos proporcionados',
    connectionFailed: 'Falló la conexión',
    loadingFailed: 'Falló la carga de datos',
    saveFailed: 'Falló al guardar cambios',
    deleteFailed: 'Falló al eliminar elemento',
    updateFailed: 'Falló al actualizar elemento',
    exportFailed: 'Falló la exportación de datos',
    importFailed: 'Falló la importación de datos',
    validationError: 'Error de validación',
    unknownError: 'Ocurrió un error desconocido'
  },

  loading: {
    general: 'Cargando...',
    robots: 'Cargando robots...',
    sessions: 'Cargando sesiones...',
    alerts: 'Cargando alertas...',
    dashboard: 'Cargando panel de control...',
    saving: 'Guardando...',
    deleting: 'Eliminando...',
    updating: 'Actualizando...',
    exporting: 'Exportando...',
    importing: 'Importando...',
    connecting: 'Conectando...'
  },

  auth: {
    testInterface: 'Interfaz de Prueba de Autenticación',
    testMode: 'Modo de Prueba',
    loginForm: 'Formulario de Inicio de Sesión',
    email: 'Correo Electrónico',
    password: 'Contraseña',
    enterEmail: 'Ingrese dirección de correo electrónico',
    enterPassword: 'Ingrese contraseña',
    testLogin: 'Probar Inicio de Sesión',
    testing: 'Probando...',
    testAccounts: 'Cuentas de Prueba',
    testResults: 'Resultados de la Prueba',
    loginSuccess: '¡Autenticación exitosa!',
    invalidCredentials: 'Correo electrónico o contraseña inválidos',
    tokenGenerated: 'Token Generado',
    userData: 'Datos del Usuario',
    copyToken: 'Copiar Token',
    tokenCopied: '¡Token Copiado!'
  }
};
